2025-07-14 22:53:20 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-14 22:53:20 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\Test4
2025-07-14 22:53:20 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-14 22:53:20 | ERROR    | root | [ERROR] CLAUDE_API_KEY environment variable is required
2025-07-14 22:53:20 | ERROR    | root | [ERROR] AIRTABLE_API_KEY environment variable is required
2025-07-14 22:53:20 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-14 22:53:20 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-14 22:53:20 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-14 22:53:20 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-14 22:54:01 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-14 22:54:01 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\Test4
2025-07-14 22:54:01 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-14 22:54:01 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-14 22:54:01 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-14 22:54:01 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-14 22:54:01 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-14 22:55:52 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:52] "GET / HTTP/1.1" 200 -
2025-07-14 22:55:52 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:52] "GET /styles.css HTTP/1.1" 200 -
2025-07-14 22:55:52 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:52] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-14 22:55:52 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:52] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-14 22:55:52 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:52] "GET /script.js HTTP/1.1" 200 -
2025-07-14 22:55:53 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:53] "GET /api/client-config HTTP/1.1" 200 -
2025-07-14 22:55:53 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:53] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1[0m" 403 -
2025-07-14 22:55:53 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:53] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL HTTP/1.1[0m" 403 -
2025-07-14 22:55:53 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:53] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblA6ABFBTURfyZx9 HTTP/1.1[0m" 403 -
2025-07-14 22:55:53 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:53] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1[0m" 403 -
2025-07-14 22:55:53 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:53] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1[0m" 403 -
2025-07-14 22:55:53 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:55:53] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL HTTP/1.1[0m" 403 -
2025-07-14 22:56:03 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 22:56:03] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-14 23:41:34 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:34] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-14 23:41:34 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:34] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-14 23:41:34 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:34] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-14 23:41:34 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:34] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-14 23:41:34 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:34] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-14 23:41:35 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:35] "GET /api/client-config HTTP/1.1" 200 -
2025-07-14 23:41:35 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:35] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1[0m" 403 -
2025-07-14 23:41:35 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:35] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL HTTP/1.1[0m" 403 -
2025-07-14 23:41:35 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:35] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblA6ABFBTURfyZx9 HTTP/1.1[0m" 403 -
2025-07-14 23:41:35 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:35] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1[0m" 403 -
2025-07-14 23:41:35 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:35] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1[0m" 403 -
2025-07-14 23:41:35 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:35] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL HTTP/1.1[0m" 403 -
2025-07-14 23:41:39 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 23:41:39] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-15 00:23:02 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 00:23:02 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\Test4
2025-07-15 00:23:02 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 00:23:02 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 00:23:02 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 00:23:02 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 00:23:02 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 00:23:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:14] "GET / HTTP/1.1" 200 -
2025-07-15 00:23:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:15] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 00:23:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:15] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 00:23:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:15] "GET /script.js HTTP/1.1" 200 -
2025-07-15 00:23:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:15] "GET /api/client-config HTTP/1.1" 200 -
2025-07-15 00:23:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:15] "[31m[1mGET /api/airtable/records?baseId=appJWDQxzECcXvWz7&tableId=tblgNNUwWlZ8iWZ0P HTTP/1.1[0m" 403 -
2025-07-15 00:23:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:16] "[31m[1mGET /api/airtable/records?baseId=appJWDQxzECcXvWz7&tableId=tblZGgaDBO9Lwl4PD HTTP/1.1[0m" 403 -
2025-07-15 00:23:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:16] "[31m[1mGET /api/airtable/records?baseId=appJWDQxzECcXvWz7&tableId=tblOg7U7VYjWTpYeu HTTP/1.1[0m" 403 -
2025-07-15 00:23:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:16] "[31m[1mGET /api/airtable/records?baseId=appJWDQxzECcXvWz7&tableId=tblZGgaDBO9Lwl4PD HTTP/1.1[0m" 403 -
2025-07-15 00:23:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:16] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 00:23:20 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:20] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 00:23:23 | INFO     | root | Server-side pagination complete: 150 total records from 2 pages
2025-07-15 00:23:23 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:23] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblA6ABFBTURfyZx9 HTTP/1.1" 200 -
2025-07-15 00:23:24 | INFO     | root | Server-side pagination complete: 43 total records from 1 pages
2025-07-15 00:23:24 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 00:23:24] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblm9kp1FStqAywPN HTTP/1.1" 200 -
