/**
 * CENTRALIZED CLIENT CONFIGURATION TEMPLATE
 * Single source of truth for all client-specific settings
 * 
 * CUSTOMIZER INSTRUCTIONS:
 * - Replace Cellular Zone with actual client name
 * - Replace app9JgRBZC2GNlaKM with client's Airtable base ID
 * - Replace table IDs with client's actual table IDs
 * - Set enabled/disabled data sources based on client needs
 * - Update locations array (will be auto-populated from GHL data)
 */

console.log('🎯 LOADING CELLULAR ZONE CONFIG FROM CORRECT FILE!');
console.log('📁 File: test8/cellular_zone_dashboard/client-config.js');
console.log('🏢 Client: Cellular Zone');
console.log('🗄️ Base ID: app9JgRBZC2GNlaKM');

window.CLIENT_CONFIG = {
    // ===== CLIENT INFORMATION =====
    clientName: 'Fix My Gadget',
    businessName: 'Fix My Gadget',
    storeName: 'Fix My Gadget', // Display name for header
    
    // ===== BUSINESS LOCATIONS =====
    // Note: Will be dynamically populated from GHL table data
    locations: [], // Auto-populated from actual GHL data
    
    // ===== AIRTABLE CONFIGURATION =====
    airtable: {
        baseId: 'app7wu1MtoDXK0UBN',
        
        // Table IDs - set to null to disable a data source
        tables: {
            ghl: 'tblBBEL7u2Rh9GkOD',                    // GHL/GoHighLevel leads
            googleAds: 'tblGNg6yPUHjWCRPt',       // Google Ads campaigns  
            pos: null,                      // Point of Sale - DISABLED
            metaAds: null,              // Meta/Facebook Ads - DISABLED
            metaAdsSimplified: null,    // Meta Ads Simplified - DISABLED
            metaAdsSummary: null,       // Meta Ads Summary - DISABLED
            metaAdsPerformance: null    // Meta Ads Performance - DISABLED
        }
    },
    
    // ===== DATA SOURCE CONFIGURATION =====
    dataSources: {
        // Enabled data sources (customizer will set based on client needs)
        enabled: ["ghl", "googleAds"],
        
        // Disabled data sources (will be hidden from UI)
        disabled: ["pos", "metaAds"],
        
        // Default date ranges for each source
        defaultDateRanges: {
            ghl: 'tblBBEL7u2Rh9GkOD',
            googleAds: 'tblGNg6yPUHjWCRPt',
            pos: null,
            metaAds: null
        }
    },
    
    // ===== UI CONFIGURATION =====
    ui: {
        // Dashboard title
        title: 'Cellular Zone Analytics Dashboard',
        
        // Show/hide sections based on enabled data sources
        showSalesReport: true,      // POS enabled
        showMetaAdsReport: true,    // Meta Ads enabled
        showGoogleAdsReport: true,  // Google Ads enabled
        showLeadReport: true,       // GHL enabled
        
        // Location-based features
        enableLocationFiltering: true,
        defaultLocation: 'all'
    }
};

// ===== HELPER FUNCTIONS =====

/**
 * Check if a data source is enabled
 */
window.CLIENT_CONFIG.isEnabled = function(dataSource) {
    return this.dataSources.enabled.includes(dataSource) && 
           this.airtable.tables[dataSource] !== null &&
           this.airtable.tables[dataSource] !== 'null' &&
           this.airtable.tables[dataSource] !== '';
};

/**
 * Get table ID for a data source
 */
window.CLIENT_CONFIG.getTableId = function(dataSource) {
    const tableId = this.airtable.tables[dataSource];
    return (tableId && tableId !== 'null' && tableId !== '') ? tableId : null;
};

/**
 * Get formatted locations with business name
 */
window.CLIENT_CONFIG.getFormattedLocations = function() {
    return this.locations.map(location => `${this.businessName} - ${location}`);
};

/**
 * Get base ID
 */
window.CLIENT_CONFIG.getBaseId = function() {
    return this.airtable.baseId;
};

/**
 * Update store name (for customizer use)
 */
window.CLIENT_CONFIG.updateStoreName = function(newStoreName) {
    this.storeName = newStoreName;
    this.businessName = newStoreName;
    this.clientName = newStoreName;

    // Update header immediately if DOM is ready
    const storeNameElement = document.getElementById('store-name');
    if (storeNameElement) {
        storeNameElement.textContent = newStoreName;
        console.log('[CONFIG] Store name updated to:', newStoreName);
    }
};

/**
 * Populate locations from GHL data
 */
window.CLIENT_CONFIG.populateLocationsFromData = function(ghlData) {
    if (!ghlData || !Array.isArray(ghlData)) {
        console.warn('[CONFIG] No GHL data provided for location population');
        return;
    }
    
    // Extract unique locations from GHL data
    const uniqueLocations = [...new Set(
        ghlData
            .map(record => record.Location || record.location)
            .filter(location => location && location.trim() !== '')
    )].sort();
    
    // Update the locations array
    this.locations = uniqueLocations;
    
    console.log('[CONFIG] Populated locations from GHL data:', this.locations);
    
    // Update location dropdowns in the UI
    this.updateLocationDropdowns();
    
    return this.locations;
};

/**
 * Update all location dropdowns in the UI
 */
window.CLIENT_CONFIG.updateLocationDropdowns = function() {
    const locationSelectors = [
        'location-filter',
        'report-location', 
        'compare-location',
        'location-performance-filter'
    ];
    
    locationSelectors.forEach(selectorId => {
        const dropdown = document.getElementById(selectorId);
        if (dropdown) {
            // Clear existing options except "All"
            dropdown.innerHTML = '<option value="all">All Locations</option>';
            
            // Add location options
            this.locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location;
                option.textContent = location;
                dropdown.appendChild(option);
            });
            
            console.log(`[CONFIG] Updated dropdown: ${selectorId} with ${this.locations.length} locations`);
        }
    });
};

// ===== CACHE MANAGEMENT =====
// Clear any cached data from previous configurations
if (typeof localStorage !== 'undefined') {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
        if (key.startsWith('rl_airtable_')) {
            localStorage.removeItem(key);
        }
    });
    console.log('[CONFIG] Cleared cached data for fresh configuration');
}

// ===== LEGACY COMPATIBILITY =====
// Keep existing AIRTABLE_CONFIG for backward compatibility
window.AIRTABLE_CONFIG = {
    baseId: window.CLIENT_CONFIG.getBaseId(),
    tables: {
        ghl: window.CLIENT_CONFIG.getTableId('ghl'),
        googleAds: window.CLIENT_CONFIG.getTableId('googleAds'),
        pos: window.CLIENT_CONFIG.getTableId('pos'),
        metaAds: window.CLIENT_CONFIG.getTableId('metaAds'),
        metaAdsSimplified: window.CLIENT_CONFIG.getTableId('metaAdsSimplified'),
        metaAdsSummary: window.CLIENT_CONFIG.getTableId('metaAdsSummary'),
        metaAdsPerformance: window.CLIENT_CONFIG.getTableId('metaAdsPerformance')
    }
};

/**
 * Tab Management Functions
 */

// Method to hide/show tabs based on configuration and data availability
window.CLIENT_CONFIG.updateTabVisibility = function(dataAvailability = {}) {
    console.log('[CONFIG] Updating tab visibility based on config and data availability');

    const tabMappings = {
        'pos': [
            // Sales Report tab button and content (POS is integrated into Sales Report)
            'button[data-tab="sales-report"]', // Tab button
            'sales-report', // Tab content
            // POS sections within Sales Report tab (actual HTML elements)
            '#locationRevenueChart', '#locationTransactionsChart',
            '.chart-card:has(#locationRevenueChart)', '.chart-card:has(#locationTransactionsChart)'
        ],
        'metaAds': [
            'button[data-tab="meta-ads-report"]', // Tab button
            'meta-ads-report' // Tab content
        ],
        'googleAds': [
            'button[data-tab="google-ads-report"]', // Tab button
            'google-ads-report' // Tab content
        ]
    };

    Object.keys(tabMappings).forEach(dataSource => {
        const shouldShow = this.shouldShowTab(dataSource, dataAvailability[dataSource]);
        const elements = tabMappings[dataSource];

        elements.forEach(selector => {
            // Handle different selector types
            let element;
            if (selector.startsWith('button[')) {
                element = document.querySelector(selector);
            } else if (selector.startsWith('.') || selector.startsWith('#')) {
                // CSS selector (class or ID)
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (shouldShow) {
                        el.style.display = '';
                        el.classList.remove('hidden');
                    } else {
                        el.style.display = 'none';
                        el.classList.add('hidden');
                        console.log(`[CONFIG] Hidden element: ${selector} (${dataSource})`);
                    }
                });
                return; // Skip the single element logic below
            } else {
                element = document.getElementById(selector);
            }

            if (element) {
                if (shouldShow) {
                    element.style.display = '';
                    element.classList.remove('hidden');
                } else {
                    element.style.display = 'none';
                    element.classList.add('hidden');
                    console.log(`[CONFIG] Hidden tab element: ${selector} (${dataSource})`);
                }
            }
        });
    });
};

// Determine if a tab should be shown based on config and data
window.CLIENT_CONFIG.shouldShowTab = function(dataSource, hasData = false) {
    const isEnabled = this.isEnabled(dataSource);

    // Hide if disabled in config
    if (!isEnabled) {
        console.log(`[CONFIG] Hiding ${dataSource} tab: disabled in configuration`);
        return false;
    }

    // Hide if enabled but no data available
    if (isEnabled && hasData === false) {
        console.log(`[CONFIG] Hiding ${dataSource} tab: enabled but no data available`);
        return false;
    }

    // Show if enabled and has data (or data status unknown)
    console.log(`[CONFIG] Showing ${dataSource} tab: enabled and has data`);
    return true;
};

// Special function to handle POS content within Sales Report
window.CLIENT_CONFIG.updateSalesReportPOSContent = function(hasData = false) {
    const shouldShow = this.shouldShowTab('pos', hasData);

    if (!shouldShow) {
        // Hide POS-specific content and show alternative message
        console.log('[CONFIG] Hiding POS content in Sales Report...');

        // Hide the Revenue by Location chart
        const revenueChart = document.getElementById('locationRevenueChart');
        if (revenueChart) {
            const revenueCard = revenueChart.closest('.chart-card');
            if (revenueCard) {
                revenueCard.style.display = 'none';
                revenueCard.classList.add('hidden');
                console.log('[CONFIG] Hidden Revenue by Location chart');
            }
        }

        // Hide the Transactions by Location chart
        const transactionsChart = document.getElementById('locationTransactionsChart');
        if (transactionsChart) {
            const transactionsCard = transactionsChart.closest('.chart-card');
            if (transactionsCard) {
                transactionsCard.style.display = 'none';
                transactionsCard.classList.add('hidden');
                console.log('[CONFIG] Hidden Transactions by Location chart');
            }
        }

        // Add a replacement message for the hidden POS content
        const chartRow = document.querySelector('#sales-report .chart-row');
        if (chartRow) {
            // Remove existing POS disabled message if any
            const existingMessage = chartRow.querySelector('.pos-disabled-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // Add a message explaining POS is not available for this client
            const posMessage = document.createElement('div');
            posMessage.className = 'pos-disabled-message chart-card';
            posMessage.style.cssText = 'text-align: center; padding: 2rem; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; margin: 1rem 0;';
            posMessage.innerHTML = `
                <h3 style="color: #6c757d; margin-bottom: 1rem;">📊 Sales Report - Lead Data Only</h3>
                <p style="color: #6c757d; margin: 0;">This dashboard is configured to show lead conversion data. POS integration is not enabled for this client.</p>
            `;

            // Add the message to replace the hidden charts
            chartRow.appendChild(posMessage);
            console.log('[CONFIG] Added POS disabled message to Sales Report');
        }

        console.log('[CONFIG] POS content hidden in Sales Report - showing lead-only view');
    }
};

// Update store name in header when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const storeNameElement = document.getElementById('store-name');
    if (storeNameElement && window.CLIENT_CONFIG.storeName) {
        storeNameElement.textContent = window.CLIENT_CONFIG.storeName;
        console.log('[CONFIG] Updated store name in header:', window.CLIENT_CONFIG.storeName);
    }
});

console.log('[CONFIG] Client configuration loaded:', window.CLIENT_CONFIG.clientName);
console.log('[CONFIG] Enabled data sources:', window.CLIENT_CONFIG.dataSources.enabled);
console.log('[CONFIG] Base ID:', window.CLIENT_CONFIG.getBaseId());
