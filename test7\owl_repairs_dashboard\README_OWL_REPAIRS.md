# Owl Repairs Analytics Dashboard

This is a customized analytics dashboard specifically configured for **Owl Repairs** business data.

## 🏗️ Configuration

### Airtable Base Configuration
- **Base ID**: `app7wu1MtoDXK0UBN`
- **Base Name**: Owl Repairs

### Enabled Data Sources
- ✅ **GHL (GoHighLevel)**
- ✅ **Google Ads**

### Table Mappings
| Data Source | Table ID | Status |
|-------------|----------|--------|
| **GHL** | `tblBBEL7u2Rh9GkOD` | ✅ Enabled |
| **Google Ads** | `tblGNg6yPUHjWCRPt` | ✅ Enabled |

### Business Locations
- East Cobb
- Kennesaw
- Marietta

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your Airtable API key
# AIRTABLE_API_KEY=your_actual_api_key_here
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run Development Server
```bash
python start_server.py
```

### 4. Access Dashboard
Open your browser to: `http://localhost:8000`

## 📊 Features

This dashboard includes only the enabled data sources for Owl Repairs:
- **GHL (GoHighLevel)**: Real-time analytics and reporting
- **Google Ads**: Real-time analytics and reporting

## 🚢 Deployment

This dashboard is configured for Railway deployment. See DEPLOYMENT.md for detailed instructions.

---

**Owl Repairs Analytics Dashboard**
*Generated by RL Dashboard Customizer*
*Enabled sources: GHL (GoHighLevel), Google Ads*
