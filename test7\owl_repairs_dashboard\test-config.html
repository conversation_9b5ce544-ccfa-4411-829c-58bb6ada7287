<!DOCTYPE html>
<html>
<head>
    <title>Test Client Configuration</title>
</head>
<body>
    <h1>Testing Client Configuration</h1>
    <div id="output"></div>
    
    <script src="client-config.js"></script>
    <script>
        try {
            document.getElementById('output').innerHTML = `
                <h2>Configuration Test Results:</h2>
                <p><strong>Client Name:</strong> ${CLIENT_CONFIG.clientName}</p>
                <p><strong>Base ID:</strong> ${CLIENT_CONFIG.getBaseId()}</p>
                <p><strong>Locations:</strong> ${CLIENT_CONFIG.locations.join(', ')}</p>
                <p><strong>GHL Enabled:</strong> ${CLIENT_CONFIG.isEnabled('ghl')}</p>
                <p><strong>POS Enabled:</strong> ${CLIENT_CONFIG.isEnabled('pos')}</p>
                <p><strong>GHL Table ID:</strong> ${CLIENT_CONFIG.getTableId('ghl')}</p>
                <p><strong>POS Table ID:</strong> ${CLIENT_CONFIG.getTableId('pos')}</p>
                <p style="color: green;"><strong>✅ Configuration loaded successfully!</strong></p>
            `;
        } catch (error) {
            document.getElementById('output').innerHTML = `
                <p style="color: red;"><strong>❌ Error:</strong> ${error.message}</p>
            `;
        }
    </script>
</body>
</html>
