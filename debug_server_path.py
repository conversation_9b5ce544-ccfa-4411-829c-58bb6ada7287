#!/usr/bin/env python3
"""
Debug script to find which client-config.js is being served
"""

import os
import glob

def find_client_configs():
    print("🔍 Searching for all client-config.js files...")
    
    # Search for all client-config.js files
    patterns = [
        "**/client-config.js",
        "*/client-config.js", 
        "client-config.js"
    ]
    
    all_configs = []
    for pattern in patterns:
        configs = glob.glob(pattern, recursive=True)
        all_configs.extend(configs)
    
    # Remove duplicates
    all_configs = list(set(all_configs))
    
    print(f"Found {len(all_configs)} client-config.js files:")
    
    for config_path in all_configs:
        print(f"\n📁 {config_path}")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for client name
            if 'Owl Repairs' in content:
                print("   ❌ Contains 'Owl Repairs'")
            elif 'Cellular Zone' in content:
                print("   ✅ Contains 'Cellular Zone'")
            elif 'Quick Fix' in content:
                print("   🔧 Contains 'Quick Fix'")
            else:
                print("   ❓ Unknown client")
                
            # Check for base ID
            if 'app7wu1MtoDXK0UBN' in content:
                print("   ❌ Contains Owl Repairs base ID")
            elif 'app9JgRBZC2GNlaKM' in content:
                print("   ✅ Contains Cellular Zone base ID")
            elif 'app7ffftdM6e3yekG' in content:
                print("   🔧 Contains Quick Fix base ID")
            else:
                print("   ❓ Unknown base ID")
                
        except Exception as e:
            print(f"   ❌ Error reading file: {e}")
    
    print("\n" + "="*60)
    print("🎯 DIAGNOSIS:")
    print("If browser shows 'Owl Repairs', you're loading the wrong file!")
    print("Make sure you're running server from: test8/cellular_zone_dashboard/")
    print("And that the server serves the correct client-config.js")

if __name__ == "__main__":
    find_client_configs()
