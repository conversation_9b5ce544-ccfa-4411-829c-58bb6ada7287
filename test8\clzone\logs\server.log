2025-07-15 00:51:49 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 00:51:49 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\rldbworking
2025-07-15 00:51:49 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 00:51:49 | INFO     | waitress | Serving on http://127.0.0.1:8000
2025-07-15 00:52:32 | INFO     | root | Server-side pagination complete: 2483 total records from 25 pages
2025-07-15 00:52:42 | INFO     | root | Server-side pagination complete: 1514 total records from 16 pages
2025-07-15 09:28:52 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 09:28:52 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\rldbworking
2025-07-15 09:28:52 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 09:28:52 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 09:28:52 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 09:28:52 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 09:28:52 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 09:42:53 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 09:42:53 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 09:42:53 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 09:42:53 | ERROR    | root | [ERROR] CLAUDE_API_KEY environment variable is required
2025-07-15 09:42:53 | ERROR    | root | [ERROR] AIRTABLE_API_KEY environment variable is required
2025-07-15 09:42:53 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 09:42:53 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 09:42:53 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 09:42:53 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 09:43:29 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 09:43:29 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 09:43:29 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 09:43:29 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 09:43:29 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 09:43:29 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 09:43:29 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 09:47:16 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 09:47:16 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 09:47:16 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 09:47:16 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 09:47:16 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 09:47:16 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 09:47:16 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 10:00:24 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 10:00:24 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 10:00:24 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 10:00:24 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 10:00:24 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 10:00:24 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 10:00:24 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 10:00:30 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 10:00:30 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 10:00:30 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 10:00:30 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 10:00:30 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 10:00:30 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 10:00:30 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 10:13:51 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 10:13:51 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 10:13:51 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 10:13:51 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 10:13:51 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 10:13:51 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 10:13:51 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 10:16:52 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 10:16:52 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 10:16:52 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 10:16:52 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://***********:8001
2025-07-15 10:16:52 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 10:17:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:16] "GET / HTTP/1.1" 200 -
2025-07-15 10:17:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:16] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 10:17:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:16] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 10:17:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:16] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 10:17:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:16] "GET /script.js HTTP/1.1" 200 -
2025-07-15 10:17:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:16] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 10:17:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:16] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:17:19 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 10:17:19 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:19] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 10:17:20 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:20] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 10:17:30 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:30] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:17:30 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:30] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 10:17:40 | INFO     | root | Server-side pagination complete: 3026 total records from 31 pages
2025-07-15 10:17:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:17:40] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 10:20:05 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:05] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 10:20:09 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:09] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 10:20:13 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:13] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:13 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:13] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:16] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:16] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:17] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:17] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:27 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:27] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 10:20:27 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:27] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 10:20:27 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:27] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:20:28 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:28] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 10:20:28 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:28] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 10:20:28 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:28] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-15 10:20:28 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:28] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:20:28 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:28] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 10:20:28 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:28] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 10:20:32 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:20:32] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 10:23:13 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:13] "GET / HTTP/1.1" 200 -
2025-07-15 10:23:13 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:13] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 10:23:13 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:13] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 10:23:13 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:13] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 10:23:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:14] "GET /script.js HTTP/1.1" 200 -
2025-07-15 10:23:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:14] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:23:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:14] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 10:23:16 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 10:23:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:16] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 10:23:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:17] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 10:23:37 | INFO     | root | Server-side pagination complete: 3026 total records from 31 pages
2025-07-15 10:23:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:23:37] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 10:27:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:44] "GET / HTTP/1.1" 200 -
2025-07-15 10:27:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:44] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 10:27:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:44] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 10:27:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:44] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 10:27:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:44] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 10:27:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:44] "GET /script.js HTTP/1.1" 200 -
2025-07-15 10:27:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:44] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 10:27:45 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:45] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 10:27:45 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:45] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:27:45 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:45] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 10:27:48 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:27:48] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 10:34:03 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 10:34:03 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 10:34:03 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 10:34:03 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://***********:8001
2025-07-15 10:34:03 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 10:34:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:29] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 10:34:30 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:30] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:34:30 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:30] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 10:34:30 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:30] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 10:34:30 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:30] "GET /script.js HTTP/1.1" 200 -
2025-07-15 10:34:32 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 10:34:32 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:32] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 10:34:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:33] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 10:34:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:33] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:34:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:33] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 10:34:53 | INFO     | root | Server-side pagination complete: 3026 total records from 31 pages
2025-07-15 10:34:53 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:53] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 10:34:53 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:34:53] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:35:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:35:59] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:35:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:35:59] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:42:20 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 10:42:20 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 10:42:20 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 10:42:20 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://***********:8001
2025-07-15 10:42:20 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 10:42:43 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:42:43] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 10:42:43 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:42:43] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:42:43 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:42:43] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 10:42:43 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:42:43] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 10:42:43 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:42:43] "GET /script.js HTTP/1.1" 200 -
2025-07-15 10:42:46 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 10:42:46 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:42:46] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 10:42:47 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:42:47] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 10:43:06 | INFO     | root | Server-side pagination complete: 3026 total records from 31 pages
2025-07-15 10:43:06 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:43:06] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 10:43:08 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:43:08] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 10:48:35 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 10:48:35 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\cellular_zone_dashboard
2025-07-15 10:48:35 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 10:48:35 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 10:48:35 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 10:48:35 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 10:48:35 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 11:03:12 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 11:03:12 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\clzone
2025-07-15 11:03:12 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 11:03:12 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 11:03:12 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 11:03:12 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 11:03:12 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 11:03:23 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:23] "GET / HTTP/1.1" 200 -
2025-07-15 11:03:24 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:24] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 11:03:24 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:24] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 11:03:24 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:24] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 11:03:24 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:24] "GET /script.js HTTP/1.1" 200 -
2025-07-15 11:03:24 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:24] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 11:03:27 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 11:03:27 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:27] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 11:03:28 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:28] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 11:03:50 | INFO     | root | Server-side pagination complete: 3026 total records from 31 pages
2025-07-15 11:03:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:50] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 11:03:51 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:03:51] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 11:04:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:04:02] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 11:04:03 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:04:03] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 11:07:50 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 11:07:50 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test8\clzone
2025-07-15 11:07:50 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 11:07:50 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://***********:8001
2025-07-15 11:07:50 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 11:08:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:14] "GET / HTTP/1.1" 200 -
2025-07-15 11:08:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:14] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 11:08:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:14] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 11:08:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:14] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 11:08:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:14] "GET /script.js HTTP/1.1" 200 -
2025-07-15 11:08:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:15] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 11:08:17 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 11:08:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:17] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 11:08:18 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:18] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 11:08:36 | INFO     | root | Server-side pagination complete: 3026 total records from 31 pages
2025-07-15 11:08:36 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:36] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 11:08:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:08:37] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 11:09:18 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:09:18] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 11:09:18 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:09:18] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 11:10:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:35] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 11:10:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:35] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 11:10:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:35] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 11:10:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:35] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 11:10:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:35] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-15 11:10:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:35] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tbl0Er1mMwZO1Pvfj HTTP/1.1" 200 -
2025-07-15 11:10:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:35] "GET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=tblIhvVihVoghHfVa HTTP/1.1" 200 -
2025-07-15 11:10:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:37] "[31m[1mGET /api/airtable/records?baseId=app9JgRBZC2GNlaKM&tableId=null HTTP/1.1[0m" 400 -
2025-07-15 11:10:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:39] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 11:10:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:55] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 11:10:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:10:55] "[36mGET /styles.css HTTP/1.1[0m" 304 -
