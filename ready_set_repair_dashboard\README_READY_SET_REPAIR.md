# Ready Set Repair Analytics Dashboard

This is a customized analytics dashboard specifically configured for **Ready Set Repair** business data. The dashboard integrates with Airtable to provide comprehensive analytics across multiple data sources.

## 🏗️ Configuration

### Airtable Base Configuration
- **Base ID**: `appJWDQxzECcXvWz7`
- **Base Name**: Ready Set Repair

### Table Mappings
| Data Source | Table ID | Table Name |
|-------------|----------|------------|
| **GHL** | `tblgNNUwWlZ8iWZ0P` | Ready Set Repair GHL |
| **POS** | `tblZGgaDBO9Lwl4PD` | Ready Set Repair POS |
| **Google Ads** | `tblOg7U7VYjWTpYeu` | Ready Set Repair Google Ads |
| **Meta Ads** | `tbloHgJm0NloiC0V8` | Ready Set Repair Meta Ads |

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your Airtable API key
# AIRTABLE_API_KEY=your_actual_api_key_here
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run Development Server
```bash
python start_server.py
```

### 4. Access Dashboard
Open your browser to: `http://localhost:8000`

## 📊 Features

### Data Sources
- **GHL (GoHighLevel)**: Lead management and pipeline data
- **POS (Point of Sale)**: Sales transactions and customer data
- **Google Ads**: Advertising campaign performance
- **Meta Ads**: Facebook/Instagram advertising data

### Dashboard Sections
1. **Master Overview**: Combined analytics across all data sources
2. **Lead Report**: GHL lead analysis and pipeline metrics
3. **Sales Report**: POS transaction analysis
4. **Google Ads Report**: Campaign performance and ROI
5. **Meta Ads Report**: Social media advertising analytics

### Key Features
- Real-time data synchronization with Airtable
- Interactive date filtering (Last 7/14/30 days, custom ranges)
- Visual charts and graphs using Chart.js
- Responsive design for desktop and mobile
- Data caching for improved performance
- Export capabilities for reports

## 🔧 Technical Details

### File Structure
```
ready_set_repair_dashboard/
├── config.py              # Configuration management
├── server.py              # Flask backend server
├── script.js              # Frontend JavaScript logic
├── index.html             # Main dashboard interface
├── styles.css             # Styling and layout
├── start_server.py        # Production server startup
├── requirements.txt       # Python dependencies
├── .env.example           # Environment template
├── Dockerfile             # Container configuration
├── railway.json           # Railway deployment config
└── DEPLOYMENT.md          # Deployment instructions
```

### API Endpoints
- `/` - Main dashboard interface
- `/api/airtable/records` - Airtable data retrieval
- `/health` - Health check endpoint
- `/healthz` - Simple health check for Railway

## 🚢 Deployment

### Railway Deployment
This dashboard is configured for easy deployment on Railway:

1. **Connect Repository**: Link your GitHub repository to Railway
2. **Environment Variables**: Set `AIRTABLE_API_KEY` in Railway dashboard
3. **Deploy**: Railway will automatically build and deploy using the included configuration

### Manual Deployment
```bash
# Build Docker image
docker build -t ready-set-repair-dashboard .

# Run container
docker run -p 8000:8000 -e AIRTABLE_API_KEY=your_key ready-set-repair-dashboard
```

## 🔐 Security

### Environment Variables
- Never commit `.env` files to version control
- Use Railway's environment variable management for production
- Rotate API keys regularly

### CORS Configuration
- Configured for localhost development
- Update `CORS_ORIGINS` in config.py for production domains

## 🐛 Troubleshooting

### Common Issues

1. **"Unauthorized" Error**
   - Check your Airtable API key in `.env`
   - Verify API key has access to the Ready Set Repair base

2. **"Table Not Found" Error**
   - Verify table IDs match your Airtable base structure
   - Check table permissions in Airtable

3. **No Data Showing**
   - Confirm tables contain data
   - Check browser console for JavaScript errors
   - Verify date filters aren't excluding all data

### Logs
- Server logs: `logs/server.log`
- Error logs: `logs/errors.log`
- Browser console: F12 → Console tab

## 📞 Support

For technical support or customization requests:
- Check the main `DEPLOYMENT.md` for detailed deployment instructions
- Review server logs for error details
- Verify Airtable base structure matches expected format

## 🔄 Updates

This dashboard was customized from the rl-test template specifically for Ready Set Repair. To update:
1. Backup current configuration
2. Apply updates from the main template
3. Restore Ready Set Repair specific configurations
4. Test all data sources and functionality

---

**Ready Set Repair Analytics Dashboard v1.0**  
*Customized for Ready Set Repair business operations*
