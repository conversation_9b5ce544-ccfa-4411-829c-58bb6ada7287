#!/usr/bin/env python3
"""
Test script to verify the centralized configuration is working
"""

print("🔧 Testing Cellular Zone Dashboard Configuration...")

try:
    print("1. Testing server-config.py import...")
    from server_config import ClientConfig
    print("✅ server-config.py imported successfully")
    
    print("2. Testing client information...")
    print(f"   Client Name: {ClientConfig.CLIENT_NAME}")
    print(f"   Business Name: {ClientConfig.BUSINESS_NAME}")
    print(f"   Base ID: {ClientConfig.AIRTABLE_BASE_ID}")
    
    print("3. Testing table IDs...")
    for source, table_id in ClientConfig.TABLE_IDS.items():
        print(f"   {source}: {table_id}")
    
    print("4. Testing enabled sources...")
    print(f"   Enabled: {ClientConfig.ENABLED_SOURCES}")
    print(f"   Disabled: {ClientConfig.DISABLED_SOURCES}")
    
    print("5. Testing fresh tables generation...")
    fresh_tables = ClientConfig.get_fresh_tables()
    for table_name, config in fresh_tables.items():
        print(f"   {table_name}: {config['id']} ({config['name']})")
    
    print("6. Testing config.py import...")
    from config import Config
    print(f"   FRESH_TABLES count: {len(Config.FRESH_TABLES)}")
    
    print("✅ All configuration tests passed!")
    print(f"🎯 Dashboard should use base ID: {ClientConfig.AIRTABLE_BASE_ID}")
    print(f"🎯 Dashboard should show client: {ClientConfig.CLIENT_NAME}")
    
except Exception as e:
    print(f"❌ Configuration test failed: {e}")
    import traceback
    traceback.print_exc()
