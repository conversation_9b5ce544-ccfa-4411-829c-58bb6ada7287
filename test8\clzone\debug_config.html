<!DOCTYPE html>
<html>
<head>
    <title>Debug Config Test</title>
</head>
<body>
    <h1>Debug Configuration Test</h1>
    <div id="output"></div>
    
    <script src="client-config.js"></script>
    <script>
        document.getElementById('output').innerHTML = `
            <h2>Configuration Debug Results:</h2>
            <p><strong>Client Name:</strong> ${CLIENT_CONFIG.clientName}</p>
            <p><strong>Base ID:</strong> ${CLIENT_CONFIG.getBaseId()}</p>
            <p><strong>GHL Table ID:</strong> ${CLIENT_CONFIG.getTableId('ghl')}</p>
            <p><strong>Google Ads Table ID:</strong> ${CLIENT_CONFIG.getTableId('googleAds')}</p>
            <p><strong>Enabled Sources:</strong> ${CLIENT_CONFIG.dataSources.enabled.join(', ')}</p>
            <p style="color: ${CLIENT_CONFIG.clientName === 'Cellular Zone' ? 'green' : 'red'};">
                <strong>${CLIENT_CONFIG.clientName === 'Cellular Zone' ? '✅ CORRECT CONFIG' : '❌ WRONG CONFIG'}</strong>
            </p>
        `;
    </script>
</body>
</html>
