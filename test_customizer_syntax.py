#!/usr/bin/env python3
"""
Test script to verify the customizer syntax is correct
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing customizer import...")
    from rl_dashboard_customizer_v2_fixed import RLDashboardCustomizerV2
    print("✅ Successfully imported RLDashboardCustomizerV2")
    
    print("Testing customizer instantiation...")
    app = RLDashboardCustomizerV2()
    print("✅ Successfully created customizer instance")
    
    print("Testing helper methods...")
    enabled_sources = app.get_enabled_sources()
    disabled_sources = app.get_disabled_sources()
    print(f"✅ Default enabled sources: {enabled_sources}")
    print(f"✅ Default disabled sources: {disabled_sources}")
    
    print("Testing validation...")
    # Set some test values
    app.template_path.set("./rldbworking")
    app.output_path.set("./test_output")
    app.client_name.set("Test Client")
    app.base_id.set("appTestBase123")
    app.ghl_table_id.set("tblTestGHL123")
    
    print("✅ All tests passed! Customizer v2.0 is working correctly.")
    print("\nTo run the GUI, execute: python rl_dashboard_customizer_v2.py")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except SyntaxError as e:
    print(f"❌ Syntax error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)

print("\n🎉 Customizer is ready to use!")
