/**
 * CENTRALIZED CLIENT CONFIGURATION
 * All client-specific settings in ONE place
 * 
 * This file contains ALL configuration for Owl Repairs
 * To customize for a different client, only modify this file
 */

window.CLIENT_CONFIG = {
    // ===== CLIENT INFORMATION =====
    clientName: 'Owl Repairs',
    businessName: 'Owl Repairs',
    
    // ===== BUSINESS LOCATIONS =====
    // Note: Locations will be dynamically populated from GHL table data
    locations: [], // Will be populated from actual GHL data
    
    // ===== AIRTABLE CONFIGURATION =====
    airtable: {
        baseId: 'app7wu1MtoDXK0UBN',
        
        // Table IDs - set to null to disable a data source
        tables: {
            ghl: 'tblBBEL7u2Rh9GkOD',           // GHL/GoHighLevel leads
            googleAds: 'tblGNg6yPUHjWCRPt',     // Google Ads campaigns  
            pos: null,                          // Point of Sale (DISABLED)
            metaAds: null,                      // Meta/Facebook Ads (DISABLED)
            metaAdsSimplified: null,            // Meta Ads Simplified (DISABLED)
            metaAdsSummary: null                // Meta Ads Summary (DISABLED)
        }
    },
    
    // ===== DATA SOURCE CONFIGURATION =====
    dataSources: {
        // Enabled data sources
        enabled: ['ghl', 'googleAds'],
        
        // Disabled data sources (will be hidden from UI)
        disabled: ['pos', 'metaAds', 'metaAdsSimplified', 'metaAdsSummary'],
        
        // Default date ranges for each source
        defaultDateRanges: {
            ghl: 'last-14',
            googleAds: 'last-30',
            pos: 'last-30',
            metaAds: 'last-30'
        }
    },
    
    // ===== UI CONFIGURATION =====
    ui: {
        // Dashboard title
        title: 'Owl Repairs Analytics Dashboard',
        
        // Show/hide sections based on enabled data sources
        showSalesReport: false,     // POS disabled
        showMetaAdsReport: false,   // Meta Ads disabled
        showGoogleAdsReport: true,  // Google Ads enabled
        showLeadReport: true,       // GHL enabled
        
        // Location-based features
        enableLocationFiltering: true,
        defaultLocation: 'all'
    },
    

};

// ===== HELPER FUNCTIONS =====

/**
 * Check if a data source is enabled
 */
window.CLIENT_CONFIG.isEnabled = function(dataSource) {
    return this.dataSources.enabled.includes(dataSource) && 
           this.airtable.tables[dataSource] !== null;
};

/**
 * Get table ID for a data source
 */
window.CLIENT_CONFIG.getTableId = function(dataSource) {
    return this.airtable.tables[dataSource];
};

/**
 * Get formatted locations with business name
 */
window.CLIENT_CONFIG.getFormattedLocations = function() {
    return this.locations.map(location => `${this.businessName} - ${location}`);
};

/**
 * Get base ID
 */
window.CLIENT_CONFIG.getBaseId = function() {
    return this.airtable.baseId;
};

/**
 * Populate locations from GHL data
 */
window.CLIENT_CONFIG.populateLocationsFromData = function(ghlData) {
    if (!ghlData || !Array.isArray(ghlData)) {
        console.warn('[CONFIG] No GHL data provided for location population');
        return;
    }

    // Extract unique locations from GHL data
    const uniqueLocations = [...new Set(
        ghlData
            .map(record => record.Location || record.location)
            .filter(location => location && location.trim() !== '')
    )].sort();

    // Update the locations array
    this.locations = uniqueLocations;

    console.log('[CONFIG] Populated locations from GHL data:', this.locations);

    // Update location dropdowns in the UI
    this.updateLocationDropdowns();

    return this.locations;
};

/**
 * Update all location dropdowns in the UI
 */
window.CLIENT_CONFIG.updateLocationDropdowns = function() {
    const locationSelectors = [
        'location-filter',
        'report-location',
        'compare-location',
        'location-performance-filter'
    ];

    locationSelectors.forEach(selectorId => {
        const dropdown = document.getElementById(selectorId);
        if (dropdown) {
            // Clear existing options except "All"
            dropdown.innerHTML = '<option value="all">All Locations</option>';

            // Add location options
            this.locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location;
                option.textContent = location;
                dropdown.appendChild(option);
            });

            console.log(`[CONFIG] Updated dropdown: ${selectorId} with ${this.locations.length} locations`);
        }
    });
};

// ===== LEGACY AIRTABLE_CONFIG FOR BACKWARD COMPATIBILITY =====
window.AIRTABLE_CONFIG = {
    baseId: window.CLIENT_CONFIG.getBaseId(),
    tables: {
        ghl: window.CLIENT_CONFIG.getTableId('ghl'),
        googleAds: window.CLIENT_CONFIG.getTableId('googleAds'),
        pos: window.CLIENT_CONFIG.getTableId('pos'),
        metaAds: window.CLIENT_CONFIG.getTableId('metaAds')
    }
};

// Clear any cached data from previous configurations
if (typeof localStorage !== 'undefined') {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
        if (key.startsWith('rl_airtable_')) {
            localStorage.removeItem(key);
        }
    });
    console.log('[CONFIG] Cleared cached data for fresh configuration');
}

console.log('[CONFIG] Client configuration loaded:', window.CLIENT_CONFIG.clientName);
console.log('[CONFIG] Enabled data sources:', window.CLIENT_CONFIG.dataSources.enabled);
console.log('[CONFIG] Business locations:', window.CLIENT_CONFIG.locations);
