#!/usr/bin/env python3
"""
RL Dashboard Customizer v3.0
Enhanced version with comprehensive verification and test8/clzone template base

Features:
- Uses test8/clzone as the base template (most advanced version)
- Dynamic tab management and store title customization
- Comprehensive verification system
- Detailed logging and error checking
- Supports all data source configurations
"""

import os
import shutil
import json
import re
import logging
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.FileHandler('dashboard_customizer_v3.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DashboardCustomizerV3:
    def __init__(self):
        self.base_template_path = Path("test8/clzone")
        self.output_base_path = Path(".")
        self.verification_results = []
        
        logger.info("🚀 RL Dashboard Customizer v3.0 initialized")
        logger.info(f"📁 Base template: {self.base_template_path}")
        
    def verify_template_exists(self):
        """Verify the base template exists and has required files"""
        logger.info("🔍 Verifying base template...")
        
        if not self.base_template_path.exists():
            raise FileNotFoundError(f"❌ Base template not found: {self.base_template_path}")
            
        required_files = [
            'client-config.js',
            'index.html', 
            'script.js',
            'server.py',
            'styles.css',
            'config.py'
        ]
        
        missing_files = []
        for file in required_files:
            file_path = self.base_template_path / file
            if not file_path.exists():
                missing_files.append(file)
                
        if missing_files:
            raise FileNotFoundError(f"❌ Missing required files: {missing_files}")
            
        logger.info("✅ Base template verification passed")
        return True
        
    def create_client_dashboard(self, client_config):
        """Create a customized dashboard for a client"""
        logger.info(f"🎯 Creating dashboard for: {client_config['client_name']}")
        
        # Validate client configuration
        self._validate_client_config(client_config)
        
        # Create output directory
        output_dir = self._create_output_directory(client_config)
        
        # Copy template files
        self._copy_template_files(output_dir)
        
        # Customize configuration
        self._customize_client_config(output_dir, client_config)
        
        # Update server configuration
        self._update_server_config(output_dir, client_config)
        
        # Verify generated dashboard
        verification_results = self._verify_generated_dashboard(output_dir, client_config)
        
        # Generate summary report
        self._generate_summary_report(output_dir, client_config, verification_results)
        
        logger.info(f"✅ Dashboard created successfully: {output_dir}")
        return output_dir, verification_results
        
    def _validate_client_config(self, config):
        """Validate client configuration with comprehensive checks"""
        logger.info("🔍 Validating client configuration...")
        
        required_fields = ['client_name', 'store_name', 'base_id', 'enabled_sources', 'table_ids']
        missing_fields = [field for field in required_fields if field not in config]
        
        if missing_fields:
            raise ValueError(f"❌ Missing required fields: {missing_fields}")
            
        # Validate base ID format
        if not re.match(r'^app[a-zA-Z0-9]{14}$', config['base_id']):
            raise ValueError(f"❌ Invalid base ID format: {config['base_id']}")
            
        # Validate table IDs
        for source, table_id in config['table_ids'].items():
            if table_id and not re.match(r'^tbl[a-zA-Z0-9]{14}$', table_id):
                raise ValueError(f"❌ Invalid table ID for {source}: {table_id}")
                
        # Validate enabled sources
        valid_sources = ['ghl', 'googleAds', 'pos', 'metaAds']
        invalid_sources = [s for s in config['enabled_sources'] if s not in valid_sources]
        if invalid_sources:
            raise ValueError(f"❌ Invalid data sources: {invalid_sources}")
            
        logger.info("✅ Client configuration validation passed")
        
    def _create_output_directory(self, config):
        """Create output directory with safe naming"""
        # Create safe directory name
        safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', config['client_name'].lower())
        safe_name = re.sub(r'_+', '_', safe_name).strip('_')
        
        output_dir = self.output_base_path / f"{safe_name}_dashboard"
        
        # Handle existing directory
        if output_dir.exists():
            backup_dir = self.output_base_path / f"{safe_name}_dashboard_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            logger.info(f"📦 Backing up existing dashboard to: {backup_dir}")
            shutil.move(str(output_dir), str(backup_dir))
            
        output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 Created output directory: {output_dir}")
        return output_dir
        
    def _copy_template_files(self, output_dir):
        """Copy all template files to output directory"""
        logger.info("📋 Copying template files...")
        
        # Files to copy (excluding certain files)
        exclude_patterns = [
            '__pycache__',
            '*.pyc',
            '.git',
            'logs',
            'test_*',
            'debug_*'
        ]
        
        def should_exclude(path):
            for pattern in exclude_patterns:
                if pattern in str(path) or path.name.startswith('test_') or path.name.startswith('debug_'):
                    return True
            return False
            
        copied_files = 0
        for item in self.base_template_path.rglob('*'):
            if item.is_file() and not should_exclude(item):
                relative_path = item.relative_to(self.base_template_path)
                dest_path = output_dir / relative_path
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(item, dest_path)
                copied_files += 1
                
        logger.info(f"✅ Copied {copied_files} template files")
        
    def _customize_client_config(self, output_dir, config):
        """Customize the client-config.js file"""
        logger.info("⚙️ Customizing client configuration...")
        
        config_file = output_dir / 'client-config.js'
        
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Update console log messages
        content = re.sub(
            r"console\.log\('🎯 LOADING [^']*'\);",
            f"console.log('🎯 LOADING {config['client_name'].upper()} CONFIG FROM CORRECT FILE!');",
            content
        )

        content = re.sub(
            r"console\.log\('🏢 Client: [^']*'\);",
            f"console.log('🏢 Client: {config['client_name']}');",
            content
        )

        content = re.sub(
            r"console\.log\('🗄️ Base ID: [^']*'\);",
            f"console.log('🗄️ Base ID: {config['base_id']}');",
            content
        )

        # Update client information
        content = re.sub(
            r"clientName: '[^']*'",
            f"clientName: '{config['client_name']}'",
            content
        )

        content = re.sub(
            r"businessName: '[^']*'",
            f"businessName: '{config['store_name']}'",
            content
        )

        content = re.sub(
            r"storeName: '[^']*'",
            f"storeName: '{config['store_name']}'",
            content
        )
        
        # Update base ID
        content = re.sub(
            r"baseId: '[^']*'",
            f"baseId: '{config['base_id']}'",
            content
        )
        
        # Update table IDs
        for source, table_id in config['table_ids'].items():
            if table_id:
                pattern = f"{source}: '[^']*'"
                replacement = f"{source}: '{table_id}'"
            else:
                pattern = f"{source}: '[^']*'"
                replacement = f"{source}: null"
            content = re.sub(pattern, replacement, content)
            
        # Update enabled sources
        enabled_sources_str = json.dumps(config['enabled_sources'])
        content = re.sub(
            r'enabled: \[[^\]]*\]',
            f'enabled: {enabled_sources_str}',
            content
        )
        
        # Update disabled sources
        all_sources = ['ghl', 'googleAds', 'pos', 'metaAds']
        disabled_sources = [s for s in all_sources if s not in config['enabled_sources']]
        disabled_sources_str = json.dumps(disabled_sources)
        content = re.sub(
            r'disabled: \[[^\]]*\]',
            f'disabled: {disabled_sources_str}',
            content
        )
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
            
        logger.info("✅ Client configuration customized")

    def _update_server_config(self, output_dir, config):
        """Update server configuration files"""
        logger.info("🔧 Updating server configuration...")

        # Update config.py if it exists
        config_py_file = output_dir / 'config.py'
        if config_py_file.exists():
            with open(config_py_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Update any hardcoded values in config.py
            content = re.sub(
                r"CLIENT_NAME = '[^']*'",
                f"CLIENT_NAME = '{config['client_name']}'",
                content
            )

            with open(config_py_file, 'w', encoding='utf-8') as f:
                f.write(content)

        logger.info("✅ Server configuration updated")

    def _verify_generated_dashboard(self, output_dir, config):
        """Comprehensive verification of generated dashboard"""
        logger.info("🔍 Verifying generated dashboard...")

        verification_results = {
            'files_exist': [],
            'config_accuracy': [],
            'table_ids_correct': [],
            'store_name_updated': [],
            'enabled_sources_correct': [],
            'errors': []
        }

        try:
            # Verify required files exist
            required_files = ['client-config.js', 'index.html', 'script.js', 'server.py', 'styles.css']
            for file in required_files:
                file_path = output_dir / file
                if file_path.exists():
                    verification_results['files_exist'].append(f"✅ {file}")
                else:
                    verification_results['files_exist'].append(f"❌ {file}")
                    verification_results['errors'].append(f"Missing file: {file}")

            # Verify client-config.js content
            config_file = output_dir / 'client-config.js'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check client name
                if f"clientName: '{config['client_name']}'" in content:
                    verification_results['config_accuracy'].append("✅ Client name updated")
                else:
                    verification_results['config_accuracy'].append("❌ Client name not updated")
                    verification_results['errors'].append("Client name not properly updated")

                # Check store name
                if f"storeName: '{config['store_name']}'" in content:
                    verification_results['store_name_updated'].append("✅ Store name updated")
                else:
                    verification_results['store_name_updated'].append("❌ Store name not updated")
                    verification_results['errors'].append("Store name not properly updated")

                # Check base ID
                if f"baseId: '{config['base_id']}'" in content:
                    verification_results['config_accuracy'].append("✅ Base ID updated")
                else:
                    verification_results['config_accuracy'].append("❌ Base ID not updated")
                    verification_results['errors'].append("Base ID not properly updated")

                # Check table IDs
                for source, table_id in config['table_ids'].items():
                    if table_id:
                        if f"{source}: '{table_id}'" in content:
                            verification_results['table_ids_correct'].append(f"✅ {source}: {table_id}")
                        else:
                            verification_results['table_ids_correct'].append(f"❌ {source}: {table_id}")
                            verification_results['errors'].append(f"Table ID for {source} not properly updated")
                    else:
                        if f"{source}: null" in content:
                            verification_results['table_ids_correct'].append(f"✅ {source}: disabled")
                        else:
                            verification_results['table_ids_correct'].append(f"❌ {source}: not disabled")
                            verification_results['errors'].append(f"Table ID for {source} not properly disabled")

                # Check enabled sources
                enabled_sources_str = json.dumps(config['enabled_sources'])
                if f'enabled: {enabled_sources_str}' in content:
                    verification_results['enabled_sources_correct'].append("✅ Enabled sources updated")
                else:
                    verification_results['enabled_sources_correct'].append("❌ Enabled sources not updated")
                    verification_results['errors'].append("Enabled sources not properly updated")

        except Exception as e:
            verification_results['errors'].append(f"Verification error: {str(e)}")
            logger.error(f"❌ Verification error: {e}")

        # Log verification summary
        total_checks = (len(verification_results['files_exist']) +
                       len(verification_results['config_accuracy']) +
                       len(verification_results['table_ids_correct']) +
                       len(verification_results['store_name_updated']) +
                       len(verification_results['enabled_sources_correct']))

        error_count = len(verification_results['errors'])
        success_count = total_checks - error_count

        logger.info(f"📊 Verification complete: {success_count}/{total_checks} checks passed")

        if error_count == 0:
            logger.info("✅ All verification checks passed!")
        else:
            logger.warning(f"⚠️ {error_count} verification issues found")

        return verification_results

    def _generate_summary_report(self, output_dir, config, verification_results):
        """Generate a comprehensive summary report"""
        logger.info("📄 Generating summary report...")

        report_content = f"""
# Dashboard Generation Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Client Configuration
- **Client Name**: {config['client_name']}
- **Store Name**: {config['store_name']}
- **Base ID**: {config['base_id']}
- **Enabled Sources**: {', '.join(config['enabled_sources'])}

## Table IDs
"""

        for source, table_id in config['table_ids'].items():
            status = "✅ Enabled" if table_id else "❌ Disabled"
            report_content += f"- **{source.upper()}**: {table_id or 'null'} {status}\n"

        report_content += f"""

## Verification Results

### Files Created
"""
        for result in verification_results['files_exist']:
            report_content += f"- {result}\n"

        report_content += "\n### Configuration Accuracy\n"
        for result in verification_results['config_accuracy']:
            report_content += f"- {result}\n"

        report_content += "\n### Table IDs\n"
        for result in verification_results['table_ids_correct']:
            report_content += f"- {result}\n"

        report_content += "\n### Store Name\n"
        for result in verification_results['store_name_updated']:
            report_content += f"- {result}\n"

        report_content += "\n### Enabled Sources\n"
        for result in verification_results['enabled_sources_correct']:
            report_content += f"- {result}\n"

        if verification_results['errors']:
            report_content += "\n## ❌ Errors Found\n"
            for error in verification_results['errors']:
                report_content += f"- {error}\n"
        else:
            report_content += "\n## ✅ No Errors Found\n"

        report_content += f"""

## Next Steps
1. Navigate to: `{output_dir}`
2. Install dependencies: `pip install -r requirements.txt`
3. Start server: `python server.py`
4. Open browser: `http://localhost:8001`

## Testing Checklist
- [ ] Dashboard loads without errors
- [ ] Store name appears in header: "{config['store_name']}"
- [ ] Only enabled tabs are visible: {', '.join(config['enabled_sources'])}
- [ ] Data loads correctly for enabled sources
- [ ] No 404 errors for disabled sources

---
Generated by RL Dashboard Customizer v3.0
"""

        report_file = output_dir / 'GENERATION_REPORT.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"✅ Summary report saved: {report_file}")


def create_test_client_dashboard():
    """Create a test dashboard for demonstration"""
    customizer = DashboardCustomizerV3()

    # Verify template exists
    customizer.verify_template_exists()

    # Test client configuration
    test_config = {
        'client_name': 'Fix My Gadget',
        'store_name': 'Fix My Gadget',
        'base_id': 'app7wu1MtoDXK0UBN',  # Example base ID
        'enabled_sources': ['ghl', 'googleAds'],  # Only enable GHL and Google Ads
        'table_ids': {
            'ghl': 'tblBBEL7u2Rh9GkOD',      # Example GHL table ID
            'googleAds': 'tblGNg6yPUHjWCRPt', # Example Google Ads table ID
            'pos': None,                       # Disabled
            'metaAds': None                    # Disabled
        }
    }

    try:
        output_dir, verification_results = customizer.create_client_dashboard(test_config)

        print(f"\n🎉 SUCCESS! Dashboard created at: {output_dir}")
        print(f"📄 Check the GENERATION_REPORT.md for detailed results")

        if verification_results['errors']:
            print(f"\n⚠️ {len(verification_results['errors'])} issues found:")
            for error in verification_results['errors']:
                print(f"   - {error}")
        else:
            print("\n✅ All verification checks passed!")

        return output_dir

    except Exception as e:
        logger.error(f"❌ Failed to create dashboard: {e}")
        raise


if __name__ == "__main__":
    print("🚀 RL Dashboard Customizer v3.0")
    print("=" * 50)

    try:
        output_dir = create_test_client_dashboard()
        print(f"\n🎯 Test dashboard created successfully!")
        print(f"📁 Location: {output_dir}")
        print(f"🌐 To test: cd {output_dir} && python server.py")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Please check the logs for more details.")
