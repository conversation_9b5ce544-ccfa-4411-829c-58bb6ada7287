/**
 * CENT<PERSON>LIZED CLIENT CONFIGURATION FOR IGENIUS REPAIR
 * Single source of truth for all client-specific settings
 *
 * Generated by RL Dashboard Customizer v3.0
 * Client: iGenius Repair
 * Base ID: apptyeo8OtplQ8wAN
 * Generated: 2025-07-15 12:11:38
 */

console.log('🎯 LOADING IGENIUS REPAIR CONFIG FROM CORRECT FILE!');
console.log('📁 File: igenius_repair_dashboard/client-config.js');
console.log('🏢 Client: iGenius Repair');
console.log('🗄️ Base ID: apptyeo8OtplQ8wAN');

// Legacy AIRTABLE_CONFIG for backward compatibility with script.js
window.AIRTABLE_CONFIG = {
    baseId: 'apptyeo8OtplQ8wAN',
    tables: {
            ghl: 'tblKWJC6YzQ7pE8xP',                           // GHL
            googleAds: 'tbl8X5hZ93IFxSOau',                     // GOOGLEADS
            pos: null,                                // POS - DISABLED
            metaAds: null,                            // METAADS - DISABLED
    }
};

window.CLIENT_CONFIG = {
    // ===== CLIENT INFORMATION =====
    clientName: 'iGenius Repair',
    businessName: 'iGenius Repair',
    storeName: 'iGenius Repair', // Display name for header

    // ===== BUSINESS LOCATIONS =====
    // Note: Will be dynamically populated from GHL table data
    locations: [], // Auto-populated from actual GHL data

    // ===== AIRTABLE CONFIGURATION =====
    airtable: {
        baseId: 'apptyeo8OtplQ8wAN',

        // Table IDs - set to null to disable a data source
        tables: {
            ghl: 'tblKWJC6YzQ7pE8xP',                           // GHL
            googleAds: 'tbl8X5hZ93IFxSOau',                     // GOOGLEADS
            pos: null,                                // POS - DISABLED
            metaAds: null,                            // METAADS - DISABLED
        }
    },

    // ===== DATA SOURCE CONFIGURATION =====
    dataSources: {
        // Enabled data sources (customizer will set based on client needs)
        enabled: ["ghl", "googleAds"],

        // Disabled data sources (will be hidden from UI)
        disabled: ["pos", "metaAds"],

        // Default date ranges for each source
        defaultDateRanges: {
            ghl: 'last-14',
            googleAds: 'last-30',
            pos: 'last-30',
            metaAds: 'last-30'
        }
    },

    // ===== HELPER FUNCTIONS =====

    /**
     * Check if a data source is enabled
     */
    isEnabled: function(dataSource) {
        return this.dataSources.enabled.includes(dataSource);
    },

    /**
     * Get table ID for a specific data source
     */
    getTableId: function(dataSource) {
        return this.airtable.tables[dataSource] || null;
    },

    /**
     * Get base ID
     */
    getBaseId: function() {
        return this.airtable.baseId;
    },

    /**
     * Update store name (for customizer use)
     */
    updateStoreName: function(newStoreName) {
        this.storeName = newStoreName;
        this.businessName = newStoreName;
        this.clientName = newStoreName;

        // Update header immediately if DOM is ready
        const storeNameElement = document.getElementById('store-name');
        if (storeNameElement) {
            storeNameElement.textContent = newStoreName;
            console.log('[CONFIG] Store name updated to:', newStoreName);
        }
    },

    /**
     * Populate location dropdowns from GHL data
     */
    populateLocations: function(ghlData) {
        if (!ghlData || !Array.isArray(ghlData)) {
            console.warn('[CONFIG] No GHL data provided for location population');
            return;
        }

        // Extract unique locations from GHL data
        const locations = [...new Set(
            ghlData
                .map(record => record.Location || record.location)
                .filter(location => location && location.trim())
        )].sort();

        this.locations = locations;
        console.log('[CONFIG] Populated locations from GHL data:', locations);

        // Update all location dropdowns
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // Clear existing options except "All Locations"
                const allOption = dropdown.querySelector('option[value="all"]');
                dropdown.innerHTML = '';
                if (allOption) {
                    dropdown.appendChild(allOption);
                }

                // Add location options
                locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location;
                    option.textContent = location;
                    dropdown.appendChild(option);
                });

                console.log(`[CONFIG] Updated dropdown: ${dropdownId} with ${locations.length} locations`);
            }
        });
    },

    /**
     * Clear cached data for fresh configuration
     */
    clearCache: function() {
        // Clear any cached data that might interfere with new configuration
        if (typeof localStorage !== 'undefined') {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('airtable') || key.includes('dashboard'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            console.log('[CONFIG] Cleared cached data for fresh configuration');
        }
    },

    // Tab visibility management
    updateTabVisibility: function(dataAvailability = {}) {
        console.log('[CONFIG] Updating tab visibility based on config and data availability');

        const tabMappings = {
            'pos': [
                // Sales Report tab button and content (POS is integrated into Sales Report)
                'button[data-tab="sales-report"]', // Tab button
                'sales-report', // Tab content
                // POS sections within Sales Report tab (actual HTML elements)
                '#locationRevenueChart', '#locationTransactionsChart',
                '.chart-card:has(#locationRevenueChart)', '.chart-card:has(#locationTransactionsChart)'
            ],
            'metaAds': [
                'button[data-tab="meta-ads-report"]', // Tab button
                'meta-ads-report' // Tab content
            ],
            'googleAds': [
                'button[data-tab="google-ads-report"]', // Tab button
                'google-ads-report' // Tab content
            ]
        };

        // Check each data source
        Object.keys(tabMappings).forEach(dataSource => {
            const isEnabled = this.isEnabled(dataSource);
            const hasData = dataAvailability[dataSource] || false;
            const shouldShow = this.shouldShowTab(dataSource, hasData);

            const elements = tabMappings[dataSource];
            this.updateTabElements(elements, shouldShow, dataSource);
        });
    },

    shouldShowTab: function(dataSource, hasData = false) {
        // Check if data source is enabled in configuration
        if (!this.isEnabled(dataSource)) {
            console.log(`[CONFIG] Hiding ${dataSource} tab: disabled in configuration`);
            return false;
        }

        // For enabled sources, check if they have data
        if (!hasData) {
            console.log(`[CONFIG] Hiding ${dataSource} tab: enabled but no data available`);
            return false;
        }

        // Show if enabled and has data (or data status unknown)
        console.log(`[CONFIG] Showing ${dataSource} tab: enabled and has data`);
        return true;
    },

    updateTabElements: function(elements, shouldShow, dataSource) {
        elements.forEach(selector => {
            // Handle different selector types
            let element;
            if (selector.startsWith('button[')) {
                element = document.querySelector(selector);
            } else if (selector.startsWith('.') || selector.startsWith('#')) {
                // CSS selector (class or ID)
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (shouldShow) {
                        el.style.display = '';
                        el.classList.remove('hidden');
                    } else {
                        el.style.display = 'none';
                        el.classList.add('hidden');
                        console.log(`[CONFIG] Hidden element: ${selector} (${dataSource})`);
                    }
                });
                return; // Skip the single element logic below
            } else {
                element = document.getElementById(selector);
            }

            if (element) {
                if (shouldShow) {
                    element.style.display = '';
                    element.classList.remove('hidden');
                } else {
                    element.style.display = 'none';
                    element.classList.add('hidden');
                    console.log(`[CONFIG] Hidden tab element: ${selector} (${dataSource})`);
                }
            }
        });
    },

    // Special function to handle POS content within Sales Report
    updateSalesReportPOSContent: function(hasData = false) {
        const shouldShow = this.shouldShowTab('pos', hasData);

        if (!shouldShow) {
            // Hide POS-specific content and show alternative message
            console.log('[CONFIG] Hiding POS content in Sales Report...');

            // Hide the Revenue by Location chart
            const revenueChart = document.getElementById('locationRevenueChart');
            if (revenueChart) {
                const revenueCard = revenueChart.closest('.chart-card');
                if (revenueCard) {
                    revenueCard.style.display = 'none';
                    revenueCard.classList.add('hidden');
                    console.log('[CONFIG] Hidden Revenue by Location chart');
                }
            }

            // Hide the Transactions by Location chart
            const transactionsChart = document.getElementById('locationTransactionsChart');
            if (transactionsChart) {
                const transactionsCard = transactionsChart.closest('.chart-card');
                if (transactionsCard) {
                    transactionsCard.style.display = 'none';
                    transactionsCard.classList.add('hidden');
                    console.log('[CONFIG] Hidden Transactions by Location chart');
                }
            }

            // Add a replacement message for the hidden POS content
            const chartRow = document.querySelector('#sales-report .chart-row');
            if (chartRow) {
                // Remove existing POS disabled message if any
                const existingMessage = chartRow.querySelector('.pos-disabled-message');
                if (existingMessage) {
                    existingMessage.remove();
                }

                // Add a message explaining POS is not available for this client
                const posMessage = document.createElement('div');
                posMessage.className = 'pos-disabled-message chart-card';
                posMessage.style.cssText = 'text-align: center; padding: 2rem; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; margin: 1rem 0;';
                posMessage.innerHTML = `
                    <h3 style="color: #6c757d; margin-bottom: 1rem;">📊 Sales Report - Lead Data Only</h3>
                    <p style="color: #6c757d; margin: 0;">This dashboard is configured to show lead conversion data. POS integration is not enabled for this client.</p>
                `;

                // Add the message to replace the hidden charts
                chartRow.appendChild(posMessage);
                console.log('[CONFIG] Added POS disabled message to Sales Report');
            }

            console.log('[CONFIG] POS content hidden in Sales Report - showing lead-only view');
        }
    }
};

// Initialize configuration when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Clear any cached data for fresh start
    window.CLIENT_CONFIG.clearCache();

    // Update store name in header when DOM is ready
    const storeNameElement = document.getElementById('store-name');
    if (storeNameElement && window.CLIENT_CONFIG.storeName) {
        storeNameElement.textContent = window.CLIENT_CONFIG.storeName;
        console.log('[CONFIG] Updated store name in header:', window.CLIENT_CONFIG.storeName);
    }
});

console.log('[CONFIG] Client configuration loaded:', window.CLIENT_CONFIG.clientName);
console.log('[CONFIG] Enabled data sources:', window.CLIENT_CONFIG.dataSources.enabled);
console.log('[CONFIG] Base ID:', window.CLIENT_CONFIG.getBaseId());
