# RL Dashboard Customizer - User Guide

## 🎯 Overview

The RL Dashboard Customizer is a comprehensive Python application that allows you to create customized analytics dashboards for different clients. It takes the rl-test template and automatically configures it with client-specific Airtable base and table IDs, business information, and data source preferences.

## 🚀 Quick Start

### 1. Launch the Application
```bash
# Option 1: Double-click the batch file
run_customizer.bat

# Option 2: Run directly with Python
python rl_dashboard_customizer.py
```

### 2. Follow the Tab-by-Tab Workflow

## 📋 Tab-by-Tab Guide

### Tab 1: Basic Config
**Purpose**: Set up fundamental project information

**Required Fields:**
- **Template Path**: Path to your rl-test template (default: `C:/Users/<USER>/Downloads/rldbworking`)
- **Output Path**: Where to create the customized dashboard (default: `C:/Users/<USER>/Downloads/RL Tools`)
- **Client Name**: Name of the client (e.g., "Ready Set Repair")
- **Business Name**: Display name for the business (e.g., "Ready Set Repair LLC")

**Tips:**
- Use the Browse buttons to select directories
- Client Name will be used in file names and configurations
- Business Name appears in the dashboard interface

### Tab 2: Airtable Config
**Purpose**: Connect to Airtable and configure table mappings

**Workflow:**
1. **Enter API Key**: Your Airtable personal access token
2. **Enter Base ID**: The Airtable base ID (starts with "app")
3. **Click "Fetch Tables"**: Automatically retrieves all tables from the base
4. **Select Tables**: Double-click tables in the list to auto-assign them

**Table Assignment:**
- **GHL Table**: GoHighLevel lead data
- **Google Ads Table**: Google advertising campaign data
- **Meta Ads Table**: Facebook/Instagram advertising data
- **POS Table**: Point of sale transaction data

**Auto-Detection:**
The app automatically detects table types based on names:
- Tables with "ghl" or "gohighlevel" → GHL
- Tables with "google" and "ads" → Google Ads
- Tables with "meta" or "facebook" → Meta Ads
- Tables with "pos" or "point" → POS

### Tab 3: Data Sources
**Purpose**: Choose which data sources to include in the dashboard

**Options:**
- ✅ **GHL (GoHighLevel)**: Lead management and pipeline data
- ✅ **Google Ads**: Advertising campaign performance (recommended)
- ⬜ **Meta Ads**: Facebook/Instagram advertising data (optional)
- ⬜ **POS (Point of Sale)**: Sales transaction data (optional)

**Recommendations:**
- **Most clients**: Enable GHL + Google Ads only
- **E-commerce clients**: Add POS if needed
- **Social media heavy**: Add Meta Ads if needed

**Impact of Disabling:**
- Disabled data sources are completely hidden from the dashboard
- Related sections in Master Overview are removed
- Navigation menu items are hidden
- No API calls are made to disabled tables

### Tab 4: Locations
**Purpose**: Configure business locations for filtering and reporting

**Features:**
- **Add Locations**: Enter location names and click "Add Location"
- **Remove Locations**: Select and click "Remove Selected"
- **Default**: "Main Location" is added automatically

**Use Cases:**
- Single location: Keep "Main Location" or rename it
- Multiple locations: Add all business locations
- Franchise: Add each franchise location

### Tab 5: Generate
**Purpose**: Validate configuration and generate the customized dashboard

**Workflow:**
1. **Click "Validate Configuration"**: Checks all settings for completeness
2. **Review Results**: Fix any ❌ issues shown
3. **Click "Generate Dashboard"**: Creates the customized dashboard
4. **Success**: Dashboard is ready for testing and deployment

## 🔧 Advanced Features

### Configuration Management
- **Save Configuration**: Export current settings to JSON file
- **Load Configuration**: Import previously saved settings
- **Reuse**: Save client configurations for future updates

### Validation System
The app performs comprehensive validation:
- ✅ Required fields are filled
- ✅ Template directory exists
- ✅ Enabled data sources have table IDs
- ✅ Airtable connectivity (when fetching tables)

### Smart Customization
The app automatically:
- Updates all base and table IDs in code files
- Hides disabled data source sections in HTML
- Creates client-specific environment files
- Generates custom README documentation
- Preserves all deployment configurations

## 📁 Generated Output

### File Structure
```
client_name_dashboard/
├── config.py              # ✅ Updated with client table IDs
├── server.py              # ✅ Updated with client base ID
├── script.js              # ✅ Updated with all client IDs
├── index.html             # ✅ Disabled sections hidden
├── .env.example           # ✅ Client-specific environment template
├── README_CLIENT.md       # ✅ Client-specific documentation
├── [all other files]     # ✅ Preserved from template
```

### What Gets Updated
1. **config.py**: FRESH_TABLES section with client table IDs
2. **server.py**: Base ID variable updated
3. **script.js**: All base and table ID references updated
4. **index.html**: Disabled data source sections hidden
5. **.env.example**: Client-specific environment variables
6. **README**: Custom documentation with client details

## 🚨 Troubleshooting

### Common Issues

**"Failed to fetch tables"**
- ✅ Check API key is correct
- ✅ Verify base ID is correct
- ✅ Ensure API key has access to the base

**"Template path does not exist"**
- ✅ Verify the rldbworking folder exists
- ✅ Use Browse button to select correct path
- ✅ Ensure template contains required files

**"Validation issues"**
- ✅ Fill all required fields
- ✅ Provide table IDs for enabled data sources
- ✅ Check that base ID starts with "app"

**"Generation failed"**
- ✅ Ensure output directory is writable
- ✅ Close any files that might be in use
- ✅ Check disk space availability

### Best Practices

1. **Always validate** before generating
2. **Save configurations** for reuse
3. **Test generated dashboards** before deployment
4. **Keep template updated** with latest rl-test version
5. **Backup configurations** for important clients

## 🎯 Typical Workflow

### For Most Clients (GHL + Google Ads Only)
1. **Basic Config**: Enter client name and business name
2. **Airtable Config**: Enter API key and base ID, fetch tables
3. **Data Sources**: Enable only GHL and Google Ads
4. **Locations**: Add business locations
5. **Generate**: Validate and generate dashboard

### For E-commerce Clients (Add POS)
1. Follow standard workflow
2. **Data Sources**: Also enable POS
3. **Airtable Config**: Assign POS table ID
4. Generate as normal

### For Social Media Heavy Clients (Add Meta Ads)
1. Follow standard workflow
2. **Data Sources**: Also enable Meta Ads
3. **Airtable Config**: Assign Meta Ads table ID
4. Generate as normal

## 📞 Support

### Generated Dashboard Issues
- Check the client-specific README file
- Verify .env configuration
- Test Airtable connectivity
- Review browser console for errors

### Customizer Issues
- Ensure Python and tkinter are installed
- Check file permissions
- Verify template integrity
- Review error messages in the validation tab

---

**RL Dashboard Customizer v1.0**  
*Streamlined dashboard customization for any client*
