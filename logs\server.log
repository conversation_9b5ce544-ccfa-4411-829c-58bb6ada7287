2025-07-15 07:40:40 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 07:40:40 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 07:40:40 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 07:40:40 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 07:40:40 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 07:40:40 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 07:40:40 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 07:41:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:02] "GET / HTTP/1.1" 200 -
2025-07-15 07:41:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:02] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 07:41:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:02] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 07:41:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:02] "GET /script.js HTTP/1.1" 200 -
2025-07-15 07:41:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:02] "[31m[1mGET /api/airtable/records?baseId=app7wuMccDXROUBN&tableId=undefined HTTP/1.1[0m" 400 -
2025-07-15 07:41:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:02] "[31m[1mGET /api/airtable/records?baseId=app7wuMccDXROUBN&tableId=tblGNg6yPUH3WCRPt HTTP/1.1[0m" 400 -
2025-07-15 07:41:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:02] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 07:41:04 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:04] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 07:41:24 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 07:41:24 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:24] "GET /api/airtable/records?baseId=appJWDQxzECcXvWz7&tableId=tblZGgaDBO9Lwl4PD HTTP/1.1" 200 -
2025-07-15 07:41:51 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:51] "GET / HTTP/1.1" 200 -
2025-07-15 07:41:52 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:52] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 07:41:52 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:52] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 07:41:52 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:52] "GET /script.js HTTP/1.1" 200 -
2025-07-15 07:41:52 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:52] "[31m[1mGET /api/airtable/records?baseId=app7wuMccDXROUBN&tableId=tblGNg6yPUH3WCRPt HTTP/1.1[0m" 400 -
2025-07-15 07:41:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:41:55] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 07:42:46 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:42:46] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 07:42:46 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:42:46] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 07:49:10 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 07:49:10 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 07:49:10 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 07:49:10 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 07:49:10 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 07:49:10 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 07:49:10 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 07:53:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:50] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 07:53:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:50] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 07:53:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:50] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 07:53:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:50] "GET /script.js HTTP/1.1" 200 -
2025-07-15 07:53:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:50] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 07:53:52 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 07:53:52 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:52] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblGNg6yPUHjWCRPt HTTP/1.1" 200 -
2025-07-15 07:53:53 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:53] "[31m[1mGET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=undefined HTTP/1.1[0m" 400 -
2025-07-15 07:53:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 07:53:55] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 08:02:41 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 08:02:41 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 08:02:41 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 08:02:41 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 08:02:41 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 08:02:41 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 08:02:41 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 08:10:04 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 08:10:04 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 08:10:04 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 08:10:04 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 08:10:04 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 08:10:04 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 08:10:04 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 08:12:27 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 08:12:27 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 08:12:27 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 08:12:27 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 08:12:27 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 08:12:27 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 08:12:27 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 08:21:58 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 08:21:58 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 08:21:58 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 08:21:58 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 08:21:58 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 08:21:58 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 08:21:58 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 08:26:39 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 08:26:39 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 08:26:39 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 08:26:39 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 08:26:39 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 08:26:39 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 08:26:39 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 08:32:57 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 08:32:57 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 08:32:57 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 08:32:57 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 08:32:57 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 08:32:57 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 08:32:57 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 08:55:05 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 08:55:05 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools
2025-07-15 08:55:05 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 08:55:05 | ERROR    | root | [ERROR] CLAUDE_API_KEY environment variable is required
2025-07-15 08:55:05 | ERROR    | root | [ERROR] AIRTABLE_API_KEY environment variable is required
2025-07-15 08:55:05 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 08:55:05 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 08:55:05 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 08:55:05 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 09:43:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:39] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 09:43:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:39] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 09:43:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:39] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 09:43:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:39] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 09:43:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:39] "GET /script.js HTTP/1.1" 200 -
2025-07-15 09:43:40 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 09:43:40 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblGNg6yPUHjWCRPt, maxRecords=None
2025-07-15 09:43:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:40] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 09:43:41 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 09:43:41 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:41] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblGNg6yPUHjWCRPt HTTP/1.1" 200 -
2025-07-15 09:43:41 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:41] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-15 09:43:50 | INFO     | root | Server-side pagination complete: 1397 total records from 14 pages
2025-07-15 09:43:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:43:50] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET / HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET /script.js HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 09:46:01 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblGNg6yPUHjWCRPt, maxRecords=None
2025-07-15 09:46:01 | INFO     | root | [CACHE] Returning cached data: 1397 records
2025-07-15 09:46:01 | INFO     | root | [CACHE] Returning cached data: 0 records
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblGNg6yPUHjWCRPt HTTP/1.1" 200 -
2025-07-15 09:46:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:01] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-15 09:46:09 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 09:46:09 | INFO     | root | [CACHE] Returning cached data: 1397 records
2025-07-15 09:46:09 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:09] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
2025-07-15 09:46:11 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 09:46:11 | INFO     | root | [CACHE] Returning cached data: 1397 records
2025-07-15 09:46:11 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:46:11] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-15 09:47:29 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 09:47:29 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblGNg6yPUHjWCRPt, maxRecords=None
2025-07-15 09:47:29 | INFO     | root | [CACHE] Returning cached data: 1397 records
2025-07-15 09:47:29 | INFO     | root | [CACHE] Returning cached data: 0 records
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblGNg6yPUHjWCRPt HTTP/1.1" 200 -
2025-07-15 09:47:29 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:29] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-15 09:47:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:40] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 09:47:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:40] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 09:47:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:40] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 09:47:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:40] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 09:47:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:40] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-15 09:47:40 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 09:47:40 | INFO     | root | [CACHE] Returning cached data: 1397 records
2025-07-15 09:47:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:40] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
2025-07-15 09:47:40 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblGNg6yPUHjWCRPt, maxRecords=None
2025-07-15 09:47:40 | INFO     | root | [CACHE] Returning cached data: 0 records
2025-07-15 09:47:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:40] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblGNg6yPUHjWCRPt HTTP/1.1" 200 -
2025-07-15 09:47:41 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:41] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 09:47:41 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 09:47:41] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-15 10:00:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:37] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 10:00:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:37] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:00:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:37] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 10:00:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:37] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 10:00:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:37] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-15 10:00:37 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 10:00:37 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblGNg6yPUHjWCRPt, maxRecords=None
2025-07-15 10:00:37 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:37] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 10:00:39 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 10:00:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:39] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblGNg6yPUHjWCRPt HTTP/1.1" 200 -
2025-07-15 10:00:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:40] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-15 10:00:49 | INFO     | root | Server-side pagination complete: 1397 total records from 14 pages
2025-07-15 10:00:49 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:00:49] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
2025-07-15 10:06:39 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:39] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-15 10:06:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:40] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 10:06:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:40] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 10:06:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:40] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 10:06:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:40] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-15 10:06:40 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblBBEL7u2Rh9GkOD, maxRecords=None
2025-07-15 10:06:40 | INFO     | root | [AIRTABLE] Request: baseId=app7wu1MtoDXK0UBN, tableId=tblGNg6yPUHjWCRPt, maxRecords=None
2025-07-15 10:06:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:40] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 10:06:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:40] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-15 10:06:41 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 10:06:41 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:41] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblGNg6yPUHjWCRPt HTTP/1.1" 200 -
2025-07-15 10:06:50 | INFO     | root | Server-side pagination complete: 1397 total records from 14 pages
2025-07-15 10:06:50 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 10:06:50] "GET /api/airtable/records?baseId=app7wu1MtoDXK0UBN&tableId=tblBBEL7u2Rh9GkOD HTTP/1.1" 200 -
