#!/usr/bin/env python3
"""
Test script to verify what the server is actually serving
"""

import requests
import time

def test_server():
    print("🔧 Testing server response...")
    
    try:
        # Test main page
        print("1. Testing main page...")
        response = requests.get('http://127.0.0.1:8000', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            if 'client-config.js' in content:
                print("   ✅ HTML contains client-config.js reference")
            else:
                print("   ❌ HTML does not contain client-config.js reference")
        
        # Test client-config.js directly
        print("2. Testing client-config.js...")
        response = requests.get('http://127.0.0.1:8000/client-config.js', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            if 'LOADING CELLULAR ZONE CONFIG' in content:
                print("   ✅ client-config.js contains debug message")
            else:
                print("   ❌ client-config.js does not contain debug message")
                
            if 'Cellular Zone' in content:
                print("   ✅ client-config.js contains 'Cellular Zone'")
            else:
                print("   ❌ client-config.js does not contain 'Cellular Zone'")
                
            if 'app9JgRBZC2GNlaKM' in content:
                print("   ✅ client-config.js contains correct base ID")
            else:
                print("   ❌ client-config.js does not contain correct base ID")
                
            # Show first few lines
            lines = content.split('\n')[:10]
            print("   📄 First 10 lines:")
            for i, line in enumerate(lines, 1):
                print(f"      {i:2d}: {line}")
        else:
            print(f"   ❌ Failed to fetch client-config.js: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Is it running on port 8000?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_server()
