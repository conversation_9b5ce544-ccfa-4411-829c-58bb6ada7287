2025-07-15 12:26:51 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 12:26:51 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test12\igenius_repair_dashboard
2025-07-15 12:26:51 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 12:26:51 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 12:26:51 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 12:26:51 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 12:26:51 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 12:26:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:55] "GET / HTTP/1.1" 200 -
2025-07-15 12:26:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:55] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 12:26:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:55] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 12:26:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:55] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 12:26:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:55] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 12:26:55 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:55] "GET /script.js HTTP/1.1" 200 -
2025-07-15 12:26:57 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 12:26:57 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:57] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tbl8X5hZ93IFxSOau HTTP/1.1" 200 -
2025-07-15 12:26:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:26:59] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 12:27:02 | INFO     | root | Server-side pagination complete: 963 total records from 10 pages
2025-07-15 12:27:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:27:02] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tblKWJC6YzQ7pE8xP HTTP/1.1" 200 -
