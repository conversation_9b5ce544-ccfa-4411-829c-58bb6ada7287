2025-07-15 11:46:36 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 11:46:36 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test9\gadget_repair_lv_dashboard
2025-07-15 11:46:36 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 11:46:36 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 11:46:36 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 11:46:36 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 11:46:36 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 11:50:51 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 11:50:51 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test9\gadget_repair_lv_dashboard
2025-07-15 11:50:51 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 11:50:51 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 11:50:51 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 11:50:51 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 11:50:51 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 11:53:25 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 11:53:25 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test9\gadget_repair_lv_dashboard
2025-07-15 11:53:25 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 11:53:25 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 11:53:25 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 11:53:25 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 11:53:25 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 11:55:47 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 11:55:47 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test9\gadget_repair_lv_dashboard
2025-07-15 11:55:47 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 11:55:47 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8002
 * Running on http://***********:8002
2025-07-15 11:55:47 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 11:56:12 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:12] "GET / HTTP/1.1" 200 -
2025-07-15 11:56:12 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:12] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 11:56:12 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:12] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 11:56:12 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:12] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 11:56:12 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:12] "GET /script.js HTTP/1.1" 200 -
2025-07-15 11:56:13 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:13] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 11:56:15 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 11:56:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:15] "GET /api/airtable/records?baseId=appL4rTljQgGkjTtp&tableId=tblF9Ej3DKJYhhl4i HTTP/1.1" 200 -
2025-07-15 11:56:16 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 11:56:16] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
