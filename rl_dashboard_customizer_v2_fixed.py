#!/usr/bin/env python3
"""
RL Dashboard Customizer v2.0 - Centralized Configuration System (FIXED)
A tool for generating client dashboards using the new centralized configuration approach
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import shutil
import re
from pathlib import Path
import requests
from typing import Dict, List, Optional
import threading
import time

class RLDashboardCustomizerV2:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("RL Dashboard Customizer v2.0 - Centralized Configuration")
        self.root.geometry("900x800")
        
        # Configuration variables
        self.template_path = tk.StringVar(value="./rldbworking")  # Default to rldbworking template
        self.output_path = tk.StringVar()
        self.client_name = tk.StringVar()
        self.business_name = tk.StringVar()
        self.base_id = tk.StringVar()
        self.airtable_api_key = tk.StringVar()
        
        # Table ID variables
        self.ghl_table_id = tk.StringVar()
        self.google_ads_table_id = tk.StringVar()
        self.pos_table_id = tk.StringVar()
        self.meta_ads_table_id = tk.StringVar()
        self.meta_ads_simplified_table_id = tk.StringVar()
        self.meta_ads_summary_table_id = tk.StringVar()
        self.meta_ads_performance_table_id = tk.StringVar()
        
        # Data source checkboxes
        self.enable_ghl = tk.BooleanVar(value=True)
        self.enable_google_ads = tk.BooleanVar(value=True)
        self.enable_pos = tk.BooleanVar(value=False)
        self.enable_meta_ads = tk.BooleanVar(value=False)
        self.enable_meta_ads_simplified = tk.BooleanVar(value=False)
        self.enable_meta_ads_summary = tk.BooleanVar(value=False)
        self.enable_meta_ads_performance = tk.BooleanVar(value=False)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: Basic Configuration
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="Basic Configuration")
        self.setup_basic_tab(basic_frame)
        
        # Tab 2: Airtable Configuration
        airtable_frame = ttk.Frame(notebook)
        notebook.add(airtable_frame, text="Airtable Configuration")
        self.setup_airtable_tab(airtable_frame)
        
        # Tab 3: Data Sources
        sources_frame = ttk.Frame(notebook)
        notebook.add(sources_frame, text="Data Sources")
        self.setup_sources_tab(sources_frame)
        
        # Tab 4: Generation & Verification
        generation_frame = ttk.Frame(notebook)
        notebook.add(generation_frame, text="Generate & Verify")
        self.setup_generation_tab(generation_frame)
        
    def setup_basic_tab(self, parent):
        """Setup basic configuration tab"""
        # Template Path
        ttk.Label(parent, text="Template Path:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.template_path, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(parent, text="Browse", command=self.browse_template).grid(row=0, column=2, padx=5, pady=5)
        
        # Output Path
        ttk.Label(parent, text="Output Path:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.output_path, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(parent, text="Browse", command=self.browse_output).grid(row=1, column=2, padx=5, pady=5)
        
        # Client Information
        ttk.Label(parent, text="Client Name:").grid(row=2, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.client_name, width=50).grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(parent, text="Business Name:").grid(row=3, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.business_name, width=50).grid(row=3, column=1, padx=5, pady=5)
        
        # Instructions
        instructions = """
CENTRALIZED CONFIGURATION APPROACH:

This new version generates dashboards using a centralized configuration system:

1. Only TWO configuration files are generated (client-config.js and server-config.py)
2. Template files are copied WITHOUT modification
3. No find/replace operations on template code
4. Much more reliable and maintainable

Template should be the rldbworking folder (clean rl-test template from GitHub) with centralized configuration support.
        """
        
        text_widget = tk.Text(parent, height=8, width=80, wrap=tk.WORD)
        text_widget.insert('1.0', instructions)
        text_widget.config(state='disabled')
        text_widget.grid(row=4, column=0, columnspan=3, padx=5, pady=10, sticky='ew')
        
    def setup_airtable_tab(self, parent):
        """Setup Airtable configuration tab"""
        # API Key
        ttk.Label(parent, text="Airtable API Key:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.airtable_api_key, width=50, show="*").grid(row=0, column=1, padx=5, pady=5)
        
        # Base ID
        ttk.Label(parent, text="Base ID:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.base_id, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(parent, text="Fetch Tables", command=self.fetch_tables).grid(row=1, column=2, padx=5, pady=5)
        
        # Table IDs
        ttk.Label(parent, text="GHL Table ID:").grid(row=2, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.ghl_table_id, width=50).grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(parent, text="Google Ads Table ID:").grid(row=3, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.google_ads_table_id, width=50).grid(row=3, column=1, padx=5, pady=5)
        
        ttk.Label(parent, text="POS Table ID:").grid(row=4, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.pos_table_id, width=50).grid(row=4, column=1, padx=5, pady=5)
        
        ttk.Label(parent, text="Meta Ads Table ID:").grid(row=5, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.meta_ads_table_id, width=50).grid(row=5, column=1, padx=5, pady=5)
        
        ttk.Label(parent, text="Meta Ads Simplified ID:").grid(row=6, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.meta_ads_simplified_table_id, width=50).grid(row=6, column=1, padx=5, pady=5)
        
        ttk.Label(parent, text="Meta Ads Summary ID:").grid(row=7, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.meta_ads_summary_table_id, width=50).grid(row=7, column=1, padx=5, pady=5)
        
        ttk.Label(parent, text="Meta Ads Performance ID:").grid(row=8, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(parent, textvariable=self.meta_ads_performance_table_id, width=50).grid(row=8, column=1, padx=5, pady=5)
        
    def setup_sources_tab(self, parent):
        """Setup data sources configuration tab"""
        ttk.Label(parent, text="Enable Data Sources:", font=('Arial', 12, 'bold')).grid(row=0, column=0, columnspan=2, pady=10)
        
        ttk.Checkbutton(parent, text="GHL (GoHighLevel)", variable=self.enable_ghl).grid(row=1, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(parent, text="Google Ads", variable=self.enable_google_ads).grid(row=2, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(parent, text="POS (Point of Sale)", variable=self.enable_pos).grid(row=3, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(parent, text="Meta Ads", variable=self.enable_meta_ads).grid(row=4, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(parent, text="Meta Ads Simplified", variable=self.enable_meta_ads_simplified).grid(row=5, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(parent, text="Meta Ads Summary", variable=self.enable_meta_ads_summary).grid(row=6, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(parent, text="Meta Ads Performance", variable=self.enable_meta_ads_performance).grid(row=7, column=0, sticky='w', padx=20, pady=5)
        
        # Instructions
        instructions = """
Data Source Configuration:

• Check the data sources that the client has available
• Only enabled sources will be included in the dashboard
• Disabled sources will be hidden from the UI
• Table IDs for disabled sources can be left empty
• GHL is typically always enabled (leads data)
• Google Ads is commonly enabled for most clients
        """
        
        text_widget = tk.Text(parent, height=8, width=60, wrap=tk.WORD)
        text_widget.insert('1.0', instructions)
        text_widget.config(state='disabled')
        text_widget.grid(row=8, column=0, columnspan=2, padx=20, pady=10, sticky='ew')
        
    def setup_generation_tab(self, parent):
        """Setup generation and verification tab"""
        # Generate button
        ttk.Button(parent, text="Generate Dashboard", command=self.generate_dashboard, 
                  style='Accent.TButton').grid(row=0, column=0, pady=20, padx=20)
        
        # Progress bar
        self.progress = ttk.Progressbar(parent, mode='indeterminate')
        self.progress.grid(row=1, column=0, columnspan=2, sticky='ew', padx=20, pady=10)
        
        # Log output
        ttk.Label(parent, text="Generation Log:").grid(row=2, column=0, sticky='w', padx=20, pady=(20,5))
        
        self.log_text = scrolledtext.ScrolledText(parent, height=20, width=80)
        self.log_text.grid(row=3, column=0, columnspan=2, padx=20, pady=5, sticky='nsew')
        
        # Configure grid weights for resizing
        parent.grid_rowconfigure(3, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
    def browse_template(self):
        """Browse for template directory"""
        path = filedialog.askdirectory(title="Select Template Directory")
        if path:
            self.template_path.set(path)
            
    def browse_output(self):
        """Browse for output directory"""
        path = filedialog.askdirectory(title="Select Output Directory")
        if path:
            self.output_path.set(path)
            
    def log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def get_enabled_sources(self):
        """Get list of enabled data sources"""
        enabled = []
        if self.enable_ghl.get():
            enabled.append('ghl')
        if self.enable_google_ads.get():
            enabled.append('googleAds')
        if self.enable_pos.get():
            enabled.append('pos')
        if self.enable_meta_ads.get():
            enabled.append('metaAds')
        if self.enable_meta_ads_simplified.get():
            enabled.append('metaAdsSimplified')
        if self.enable_meta_ads_summary.get():
            enabled.append('metaAdsSummary')
        if self.enable_meta_ads_performance.get():
            enabled.append('metaAdsPerformance')
        return enabled
        
    def get_disabled_sources(self):
        """Get list of disabled data sources"""
        all_sources = ['ghl', 'googleAds', 'pos', 'metaAds', 'metaAdsSimplified', 'metaAdsSummary', 'metaAdsPerformance']
        enabled = self.get_enabled_sources()
        return [source for source in all_sources if source not in enabled]
        
    def fetch_tables(self):
        """Fetch table information from Airtable"""
        if not self.base_id.get() or not self.airtable_api_key.get():
            messagebox.showerror("Error", "Please enter Base ID and API Key first")
            return
            
        self.log("Fetching tables from Airtable...")
        
        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key.get()}',
                'Content-Type': 'application/json'
            }
            
            url = f"https://api.airtable.com/v0/meta/bases/{self.base_id.get()}/tables"
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                tables = data.get('tables', [])
                
                self.log(f"Found {len(tables)} tables:")
                for table in tables:
                    self.log(f"  - {table['name']}: {table['id']}")
                    
                # Auto-populate common table IDs based on name matching
                self.auto_populate_table_ids(tables)
                
            else:
                self.log(f"Error fetching tables: {response.status_code} - {response.text}")
                messagebox.showerror("Error", f"Failed to fetch tables: {response.status_code}")
                
        except Exception as e:
            self.log(f"Exception fetching tables: {str(e)}")
            messagebox.showerror("Error", f"Exception: {str(e)}")
            
    def auto_populate_table_ids(self, tables):
        """Auto-populate table IDs based on name matching"""
        name_mappings = {
            'ghl': ['ghl', 'gohighlevel', 'leads'],
            'google_ads': ['google ads', 'google_ads', 'googleads', 'adwords'],
            'pos': ['pos', 'point of sale', 'sales'],
            'meta_ads': ['meta ads', 'meta_ads', 'facebook ads', 'fb ads'],
            'meta_ads_simplified': ['meta ads simplified', 'meta_simplified'],
            'meta_ads_summary': ['meta ads summary', 'meta_summary'],
            'meta_ads_performance': ['meta ads performance', 'meta_performance']
        }
        
        for table in tables:
            table_name_lower = table['name'].lower()
            
            for field_name, possible_names in name_mappings.items():
                if any(name in table_name_lower for name in possible_names):
                    if field_name == 'ghl':
                        self.ghl_table_id.set(table['id'])
                    elif field_name == 'google_ads':
                        self.google_ads_table_id.set(table['id'])
                    elif field_name == 'pos':
                        self.pos_table_id.set(table['id'])
                    elif field_name == 'meta_ads':
                        self.meta_ads_table_id.set(table['id'])
                    elif field_name == 'meta_ads_simplified':
                        self.meta_ads_simplified_table_id.set(table['id'])
                    elif field_name == 'meta_ads_summary':
                        self.meta_ads_summary_table_id.set(table['id'])
                    elif field_name == 'meta_ads_performance':
                        self.meta_ads_performance_table_id.set(table['id'])
                    
                    self.log(f"Auto-populated {field_name}: {table['name']} ({table['id']})")
                    break
                    
    def generate_dashboard(self):
        """Generate the client dashboard"""
        # Validate inputs
        if not self.validate_inputs():
            return
            
        # Start generation in separate thread
        self.progress.start()
        thread = threading.Thread(target=self._generate_dashboard_thread)
        thread.daemon = True
        thread.start()
        
    def validate_inputs(self):
        """Validate all required inputs"""
        if not self.template_path.get():
            messagebox.showerror("Error", "Please select template path")
            return False
            
        if not self.output_path.get():
            messagebox.showerror("Error", "Please select output path")
            return False
            
        if not self.client_name.get():
            messagebox.showerror("Error", "Please enter client name")
            return False
            
        if not self.base_id.get():
            messagebox.showerror("Error", "Please enter Airtable base ID")
            return False
            
        # Check that at least one data source is enabled
        enabled_sources = self.get_enabled_sources()
        if not enabled_sources:
            messagebox.showerror("Error", "Please enable at least one data source")
            return False
            
        # Check that enabled sources have table IDs
        missing_tables = []
        if self.enable_ghl.get() and not self.ghl_table_id.get():
            missing_tables.append("GHL")
        if self.enable_google_ads.get() and not self.google_ads_table_id.get():
            missing_tables.append("Google Ads")
        if self.enable_pos.get() and not self.pos_table_id.get():
            missing_tables.append("POS")
        if self.enable_meta_ads.get() and not self.meta_ads_table_id.get():
            missing_tables.append("Meta Ads")
            
        if missing_tables:
            messagebox.showerror("Error", f"Missing table IDs for enabled sources: {', '.join(missing_tables)}")
            return False
            
        return True

    def _generate_dashboard_thread(self):
        """Main generation thread"""
        try:
            self.log("=" * 60)
            self.log("STARTING DASHBOARD GENERATION")
            self.log("=" * 60)

            # Step 1: Create output directory
            self.log("Step 1: Creating output directory...")
            output_dir = os.path.join(self.output_path.get(), f"{self.client_name.get().lower().replace(' ', '_')}_dashboard")
            os.makedirs(output_dir, exist_ok=True)
            self.log(f"Created: {output_dir}")

            # Step 2: Copy template files
            self.log("Step 2: Copying template files...")
            self.copy_template_files(output_dir)

            # Step 3: Generate client-config.js
            self.log("Step 3: Generating client-config.js...")
            self.generate_client_config(output_dir)

            # Step 4: Generate server-config.py
            self.log("Step 4: Generating server-config.py...")
            self.generate_server_config(output_dir)

            # Step 5: Verify generated files
            self.log("Step 5: Verifying generated files...")
            self.verify_generated_files(output_dir)

            self.log("=" * 60)
            self.log("DASHBOARD GENERATION COMPLETE!")
            self.log("=" * 60)
            self.log(f"Dashboard created at: {output_dir}")

            messagebox.showinfo("Success", f"Dashboard generated successfully!\n\nLocation: {output_dir}")

        except Exception as e:
            self.log(f"ERROR: {str(e)}")
            messagebox.showerror("Error", f"Generation failed: {str(e)}")
        finally:
            self.progress.stop()

    def copy_template_files(self, output_dir):
        """Copy template files to output directory"""
        template_files = [
            'script.js',
            'config.py',
            'server.py',
            'index.html',
            'styles.css',
            'main.py',
            'requirements.txt',
            'Dockerfile',
            'Procfile',
            'railway.json',
            'runtime.txt'
        ]

        # Copy directories
        template_dirs = ['img', 'logs']

        for file_name in template_files:
            src = os.path.join(self.template_path.get(), file_name)
            dst = os.path.join(output_dir, file_name)

            if os.path.exists(src):
                shutil.copy2(src, dst)
                self.log(f"Copied: {file_name}")
            else:
                self.log(f"Warning: Template file not found: {file_name}")

        for dir_name in template_dirs:
            src = os.path.join(self.template_path.get(), dir_name)
            dst = os.path.join(output_dir, dir_name)

            if os.path.exists(src):
                shutil.copytree(src, dst, dirs_exist_ok=True)
                self.log(f"Copied directory: {dir_name}")
            else:
                self.log(f"Warning: Template directory not found: {dir_name}")

    def generate_client_config(self, output_dir):
        """Generate client-config.js file"""
        # Read template
        template_path = os.path.join(self.template_path.get(), 'client-config.template.js')
        if not os.path.exists(template_path):
            raise Exception("client-config.template.js not found in template directory")

        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()

        # Replace placeholders
        config_content = template_content.replace('CLIENT_NAME', self.client_name.get())
        config_content = config_content.replace('CLIENT_BASE_ID', self.base_id.get())

        # Replace table IDs
        config_content = config_content.replace('CLIENT_GHL_TABLE_ID',
                                              self.ghl_table_id.get() if self.enable_ghl.get() else 'null')
        config_content = config_content.replace('CLIENT_GOOGLE_ADS_TABLE_ID',
                                              self.google_ads_table_id.get() if self.enable_google_ads.get() else 'null')
        config_content = config_content.replace('CLIENT_POS_TABLE_ID',
                                              self.pos_table_id.get() if self.enable_pos.get() else 'null')
        config_content = config_content.replace('CLIENT_META_ADS_TABLE_ID',
                                              self.meta_ads_table_id.get() if self.enable_meta_ads.get() else 'null')
        config_content = config_content.replace('CLIENT_META_ADS_SIMPLIFIED_TABLE_ID',
                                              self.meta_ads_simplified_table_id.get() if self.enable_meta_ads_simplified.get() else 'null')
        config_content = config_content.replace('CLIENT_META_ADS_SUMMARY_TABLE_ID',
                                              self.meta_ads_summary_table_id.get() if self.enable_meta_ads_summary.get() else 'null')
        config_content = config_content.replace('CLIENT_META_ADS_PERFORMANCE_TABLE_ID',
                                              self.meta_ads_performance_table_id.get() if self.enable_meta_ads_performance.get() else 'null')

        # Replace enabled/disabled sources
        enabled_sources = self.get_enabled_sources()
        disabled_sources = self.get_disabled_sources()

        # Format the arrays properly for JavaScript (using double quotes)
        enabled_sources_js = str(enabled_sources).replace("'", '"')
        disabled_sources_js = str(disabled_sources).replace("'", '"')

        config_content = config_content.replace("enabled: ['ghl', 'googleAds', 'pos', 'metaAds']",
                                              f"enabled: {enabled_sources_js}")
        config_content = config_content.replace("disabled: []",
                                              f"disabled: {disabled_sources_js}")

        # Write file
        output_path = os.path.join(output_dir, 'client-config.js')
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(config_content)

        self.log(f"Generated: client-config.js")
        self.log(f"  - Client: {self.client_name.get()}")
        self.log(f"  - Base ID: {self.base_id.get()}")
        self.log(f"  - Enabled sources: {enabled_sources}")

    def generate_server_config(self, output_dir):
        """Generate server-config.py file"""
        # Read template
        template_path = os.path.join(self.template_path.get(), 'server-config.template.py')
        if not os.path.exists(template_path):
            raise Exception("server-config.template.py not found in template directory")

        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()

        # Replace placeholders
        config_content = template_content.replace('CLIENT_NAME', self.client_name.get())
        config_content = config_content.replace('CLIENT_BASE_ID', self.base_id.get())

        # Replace table IDs
        config_content = config_content.replace('CLIENT_GHL_TABLE_ID',
                                              f"'{self.ghl_table_id.get()}'" if self.enable_ghl.get() else 'None')
        config_content = config_content.replace('CLIENT_GOOGLE_ADS_TABLE_ID',
                                              f"'{self.google_ads_table_id.get()}'" if self.enable_google_ads.get() else 'None')
        config_content = config_content.replace('CLIENT_POS_TABLE_ID',
                                              f"'{self.pos_table_id.get()}'" if self.enable_pos.get() else 'None')
        config_content = config_content.replace('CLIENT_META_ADS_TABLE_ID',
                                              f"'{self.meta_ads_table_id.get()}'" if self.enable_meta_ads.get() else 'None')
        config_content = config_content.replace('CLIENT_META_ADS_SIMPLIFIED_TABLE_ID',
                                              f"'{self.meta_ads_simplified_table_id.get()}'" if self.enable_meta_ads_simplified.get() else 'None')
        config_content = config_content.replace('CLIENT_META_ADS_SUMMARY_TABLE_ID',
                                              f"'{self.meta_ads_summary_table_id.get()}'" if self.enable_meta_ads_summary.get() else 'None')
        config_content = config_content.replace('CLIENT_META_ADS_PERFORMANCE_TABLE_ID',
                                              f"'{self.meta_ads_performance_table_id.get()}'" if self.enable_meta_ads_performance.get() else 'None')

        # Replace enabled sources (Python format)
        enabled_sources_py = []
        for src in self.get_enabled_sources():
            if src == 'googleAds':
                enabled_sources_py.append('google_ads')
            elif src == 'metaAds':
                enabled_sources_py.append('meta_ads')
            elif src == 'metaAdsSimplified':
                enabled_sources_py.append('meta_ads_simplified')
            elif src == 'metaAdsSummary':
                enabled_sources_py.append('meta_ads_summary')
            elif src == 'metaAdsPerformance':
                enabled_sources_py.append('meta_ads_performance')
            else:
                enabled_sources_py.append(src)

        config_content = config_content.replace("ENABLED_SOURCES = ['ghl', 'google_ads', 'pos', 'meta_ads']",
                                              f"ENABLED_SOURCES = {enabled_sources_py}")

        # Write file
        output_path = os.path.join(output_dir, 'server-config.py')
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(config_content)

        self.log(f"Generated: server-config.py")
        self.log(f"  - Python format configuration")
        self.log(f"  - Enabled sources: {enabled_sources_py}")

    def verify_generated_files(self, output_dir):
        """Verify that all required files were generated correctly"""
        required_files = [
            'client-config.js',
            'server-config.py',
            'script.js',
            'config.py',
            'server.py',
            'index.html'
        ]

        missing_files = []
        for file_name in required_files:
            file_path = os.path.join(output_dir, file_name)
            if not os.path.exists(file_path):
                missing_files.append(file_name)

        if missing_files:
            raise Exception(f"Missing required files: {', '.join(missing_files)}")

        self.log("✅ All required files present")

        # Verify configuration files contain correct data
        self.verify_client_config(output_dir)
        self.verify_server_config(output_dir)

    def verify_client_config(self, output_dir):
        """Verify client-config.js contains correct configuration"""
        config_path = os.path.join(output_dir, 'client-config.js')

        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for client name
        if self.client_name.get() not in content:
            raise Exception("Client name not found in client-config.js")

        # Check for base ID
        if self.base_id.get() not in content:
            raise Exception("Base ID not found in client-config.js")

        # Check for enabled table IDs
        if self.enable_ghl.get() and self.ghl_table_id.get() not in content:
            raise Exception("GHL table ID not found in client-config.js")

        if self.enable_google_ads.get() and self.google_ads_table_id.get() not in content:
            raise Exception("Google Ads table ID not found in client-config.js")

        self.log("✅ client-config.js verification passed")

    def verify_server_config(self, output_dir):
        """Verify server-config.py contains correct configuration"""
        config_path = os.path.join(output_dir, 'server-config.py')

        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for client name
        if self.client_name.get() not in content:
            raise Exception("Client name not found in server-config.py")

        # Check for base ID
        if self.base_id.get() not in content:
            raise Exception("Base ID not found in server-config.py")

        self.log("✅ server-config.py verification passed")

    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = RLDashboardCustomizerV2()
    app.run()
