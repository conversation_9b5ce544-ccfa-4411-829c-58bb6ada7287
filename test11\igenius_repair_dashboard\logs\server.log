2025-07-15 12:11:55 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 12:11:55 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test11\igenius_repair_dashboard
2025-07-15 12:11:55 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 12:11:55 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 12:11:55 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 12:11:55 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 12:11:55 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 12:12:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:02] "GET / HTTP/1.1" 200 -
2025-07-15 12:12:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:02] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 12:12:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:02] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 12:12:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:02] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 12:12:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:02] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 12:12:02 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:02] "GET /script.js HTTP/1.1" 200 -
2025-07-15 12:12:04 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 12:12:04 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:04] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tbl8X5hZ93IFxSOau HTTP/1.1" 200 -
2025-07-15 12:12:07 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:07] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 12:12:10 | INFO     | root | Server-side pagination complete: 963 total records from 10 pages
2025-07-15 12:12:10 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:10] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tblKWJC6YzQ7pE8xP HTTP/1.1" 200 -
2025-07-15 12:12:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:33] "GET / HTTP/1.1" 200 -
2025-07-15 12:12:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:33] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 12:12:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:33] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 12:12:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:33] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 12:12:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:33] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 12:12:33 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:33] "GET /script.js HTTP/1.1" 200 -
2025-07-15 12:12:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:35] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tblKWJC6YzQ7pE8xP HTTP/1.1" 200 -
2025-07-15 12:12:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:35] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tbl8X5hZ93IFxSOau HTTP/1.1" 200 -
2025-07-15 12:12:38 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:12:38] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 12:17:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:44] "GET / HTTP/1.1" 200 -
2025-07-15 12:17:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:44] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 12:17:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:44] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 12:17:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:44] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 12:17:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:44] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 12:17:44 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:44] "GET /script.js HTTP/1.1" 200 -
2025-07-15 12:17:47 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 12:17:47 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:47] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tbl8X5hZ93IFxSOau HTTP/1.1" 200 -
2025-07-15 12:17:48 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:48] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 12:17:52 | INFO     | root | Server-side pagination complete: 963 total records from 10 pages
2025-07-15 12:17:52 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:17:52] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tblKWJC6YzQ7pE8xP HTTP/1.1" 200 -
2025-07-15 12:18:35 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 12:18:35 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test11\igenius_repair_dashboard
2025-07-15 12:18:35 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 12:18:35 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 12:18:35 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 12:18:35 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 12:18:35 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 12:18:49 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:49] "GET / HTTP/1.1" 200 -
2025-07-15 12:18:49 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:49] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 12:18:49 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:49] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 12:18:49 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:49] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 12:18:49 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:49] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 12:18:49 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:49] "GET /script.js HTTP/1.1" 200 -
2025-07-15 12:18:52 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-15 12:18:52 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:52] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tbl8X5hZ93IFxSOau HTTP/1.1" 200 -
2025-07-15 12:18:54 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:54] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 12:18:58 | INFO     | root | Server-side pagination complete: 963 total records from 10 pages
2025-07-15 12:18:58 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:18:58] "GET /api/airtable/records?baseId=apptyeo8OtplQ8wAN&tableId=tblKWJC6YzQ7pE8xP HTTP/1.1" 200 -
