#!/usr/bin/env python3
"""
RL Dashboard Customizer
A comprehensive tool for customizing rl-test template dashboards for different clients
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import shutil
import re
from pathlib import Path
import requests
from typing import Dict, List, Optional

class RLDashboardCustomizer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("RL Dashboard Customizer")
        self.root.geometry("800x900")
        
        # Configuration variables
        self.template_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.client_name = tk.StringVar()
        self.business_name = tk.StringVar()
        self.base_id = tk.StringVar()
        self.airtable_api_key = tk.StringVar()
        
        # Table ID variables
        self.ghl_table_id = tk.StringVar()
        self.google_ads_table_id = tk.StringVar()
        self.meta_ads_table_id = tk.StringVar()
        self.pos_table_id = tk.StringVar()
        
        # Data source toggles
        self.enable_ghl = tk.BooleanVar(value=True)
        self.enable_google_ads = tk.BooleanVar(value=True)
        self.enable_meta_ads = tk.BooleanVar(value=False)
        self.enable_pos = tk.BooleanVar(value=False)
        
        # Location management
        self.locations = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: Basic Configuration
        self.setup_basic_config_tab(notebook)
        
        # Tab 2: Airtable Configuration
        self.setup_airtable_config_tab(notebook)
        
        # Tab 3: Data Sources
        self.setup_data_sources_tab(notebook)
        
        # Tab 4: Locations
        self.setup_locations_tab(notebook)
        
        # Tab 5: Generate
        self.setup_generate_tab(notebook)
        
    def setup_basic_config_tab(self, notebook):
        """Setup basic configuration tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Basic Config")
        
        # Template Path
        ttk.Label(frame, text="Template Path:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.template_path, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(frame, text="Browse", command=self.browse_template).grid(row=0, column=2, padx=5, pady=5)
        
        # Output Path
        ttk.Label(frame, text="Output Path:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.output_path, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(frame, text="Browse", command=self.browse_output).grid(row=1, column=2, padx=5, pady=5)
        
        # Client Name
        ttk.Label(frame, text="Client Name:").grid(row=2, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.client_name, width=50).grid(row=2, column=1, padx=5, pady=5)
        
        # Business Name
        ttk.Label(frame, text="Business Name:").grid(row=3, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.business_name, width=50).grid(row=3, column=1, padx=5, pady=5)
        
        # Set defaults
        self.template_path.set("C:/Users/<USER>/Downloads/rldbworking")
        self.output_path.set("C:/Users/<USER>/Downloads/RL Tools")
        
    def setup_airtable_config_tab(self, notebook):
        """Setup Airtable configuration tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Airtable Config")
        
        # API Key
        ttk.Label(frame, text="Airtable API Key:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.airtable_api_key, width=50, show="*").grid(row=0, column=1, padx=5, pady=5)
        
        # Base ID
        ttk.Label(frame, text="Base ID:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.base_id, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(frame, text="Fetch Tables", command=self.fetch_tables).grid(row=1, column=2, padx=5, pady=5)
        
        # Table IDs
        ttk.Label(frame, text="GHL Table ID:").grid(row=2, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.ghl_table_id, width=50).grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(frame, text="Google Ads Table ID:").grid(row=3, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.google_ads_table_id, width=50).grid(row=3, column=1, padx=5, pady=5)
        
        ttk.Label(frame, text="Meta Ads Table ID:").grid(row=4, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.meta_ads_table_id, width=50).grid(row=4, column=1, padx=5, pady=5)
        
        ttk.Label(frame, text="POS Table ID:").grid(row=5, column=0, sticky='w', padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.pos_table_id, width=50).grid(row=5, column=1, padx=5, pady=5)
        
        # Table list display
        ttk.Label(frame, text="Available Tables:").grid(row=6, column=0, sticky='nw', padx=5, pady=5)
        self.tables_listbox = tk.Listbox(frame, height=8, width=70)
        self.tables_listbox.grid(row=6, column=1, columnspan=2, padx=5, pady=5)
        self.tables_listbox.bind('<Double-Button-1>', self.on_table_select)
        
    def setup_data_sources_tab(self, notebook):
        """Setup data sources configuration tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Data Sources")
        
        ttk.Label(frame, text="Enable Data Sources:", font=('Arial', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=10)
        
        # Data source checkboxes
        ttk.Checkbutton(frame, text="GHL (GoHighLevel)", variable=self.enable_ghl).grid(row=1, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(frame, text="Google Ads", variable=self.enable_google_ads).grid(row=2, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(frame, text="Meta Ads", variable=self.enable_meta_ads).grid(row=3, column=0, sticky='w', padx=20, pady=5)
        ttk.Checkbutton(frame, text="POS (Point of Sale)", variable=self.enable_pos).grid(row=4, column=0, sticky='w', padx=20, pady=5)
        
        # Information labels
        ttk.Label(frame, text="Note: Unchecked data sources will be hidden from the dashboard", 
                 foreground='blue').grid(row=5, column=0, sticky='w', padx=5, pady=10)
        
        ttk.Label(frame, text="Recommended: GHL + Google Ads for most clients", 
                 foreground='green').grid(row=6, column=0, sticky='w', padx=5, pady=5)
        
    def setup_locations_tab(self, notebook):
        """Setup locations management tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Locations")
        
        ttk.Label(frame, text="Business Locations:", font=('Arial', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=10)
        
        # Location entry
        ttk.Label(frame, text="Location Name:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.location_entry = ttk.Entry(frame, width=40)
        self.location_entry.grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(frame, text="Add Location", command=self.add_location).grid(row=1, column=2, padx=5, pady=5)
        
        # Locations list
        ttk.Label(frame, text="Current Locations:").grid(row=2, column=0, sticky='nw', padx=5, pady=5)
        self.locations_listbox = tk.Listbox(frame, height=8, width=50)
        self.locations_listbox.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Button(frame, text="Remove Selected", command=self.remove_location).grid(row=2, column=2, padx=5, pady=5, sticky='n')
        
        # Add default location
        self.locations = ["Main Location"]
        self.update_locations_display()
        
    def setup_generate_tab(self, notebook):
        """Setup generation tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Generate")
        
        ttk.Label(frame, text="Dashboard Generation", font=('Arial', 14, 'bold')).grid(row=0, column=0, pady=20)
        
        # Validation status
        self.validation_text = tk.Text(frame, height=15, width=80)
        self.validation_text.grid(row=1, column=0, padx=10, pady=10)
        
        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, pady=10)
        
        ttk.Button(button_frame, text="Validate Configuration", command=self.validate_config).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Generate Dashboard", command=self.generate_dashboard).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Save Configuration", command=self.save_config).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Load Configuration", command=self.load_config).pack(side='left', padx=5)
        
    def browse_template(self):
        """Browse for template directory"""
        path = filedialog.askdirectory(title="Select Template Directory")
        if path:
            self.template_path.set(path)
            
    def browse_output(self):
        """Browse for output directory"""
        path = filedialog.askdirectory(title="Select Output Directory")
        if path:
            self.output_path.set(path)
            
    def fetch_tables(self):
        """Fetch tables from Airtable base and auto-assign if possible"""
        if not self.base_id.get() or not self.airtable_api_key.get():
            messagebox.showerror("Error", "Please enter Base ID and API Key")
            return

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key.get()}',
                'Content-Type': 'application/json'
            }

            url = f'https://api.airtable.com/v0/meta/bases/{self.base_id.get()}/tables'
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                tables = data.get('tables', [])

                self.tables_listbox.delete(0, tk.END)

                # Auto-assign tables based on names
                auto_assigned = []

                for table in tables:
                    table_info = f"{table['name']} - {table['id']}"
                    self.tables_listbox.insert(tk.END, table_info)

                    table_name_lower = table['name'].lower()
                    table_id = table['id']

                    # Auto-assign based on table name
                    if ('ghl' in table_name_lower or 'gohighlevel' in table_name_lower) and not self.ghl_table_id.get():
                        self.ghl_table_id.set(table_id)
                        auto_assigned.append(f"GHL: {table['name']}")

                    elif ('google' in table_name_lower and 'ads' in table_name_lower) and not self.google_ads_table_id.get():
                        self.google_ads_table_id.set(table_id)
                        auto_assigned.append(f"Google Ads: {table['name']}")

                    elif ('meta' in table_name_lower or 'facebook' in table_name_lower) and not self.meta_ads_table_id.get():
                        self.meta_ads_table_id.set(table_id)
                        auto_assigned.append(f"Meta Ads: {table['name']}")

                    elif ('pos' in table_name_lower or 'point' in table_name_lower or 'sale' in table_name_lower) and not self.pos_table_id.get():
                        self.pos_table_id.set(table_id)
                        auto_assigned.append(f"POS: {table['name']}")

                success_msg = f"Fetched {len(tables)} tables"
                if auto_assigned:
                    success_msg += f"\n\nAuto-assigned:\n" + "\n".join(auto_assigned)

                messagebox.showinfo("Success", success_msg)

                # Validate the assigned IDs
                self.validate_table_ids()

            else:
                messagebox.showerror("Error", f"Failed to fetch tables: {response.status_code}")

        except Exception as e:
            messagebox.showerror("Error", f"Error fetching tables: {str(e)}")

    def validate_table_ids(self):
        """Validate that table IDs are correct format"""
        issues = []

        # Check base ID format
        base_id = self.base_id.get()
        if base_id and (not base_id.startswith('app') or len(base_id) != 17):
            issues.append(f"Base ID should be 17 characters starting with 'app', got: {base_id} ({len(base_id)} chars)")

        # Check table ID formats
        table_checks = [
            ("GHL", self.ghl_table_id.get(), self.enable_ghl.get()),
            ("Google Ads", self.google_ads_table_id.get(), self.enable_google_ads.get()),
            ("Meta Ads", self.meta_ads_table_id.get(), self.enable_meta_ads.get()),
            ("POS", self.pos_table_id.get(), self.enable_pos.get())
        ]

        for name, table_id, enabled in table_checks:
            if enabled and table_id:
                if not table_id.startswith('tbl'):
                    issues.append(f"{name} table ID should start with 'tbl', got: {table_id}")
                elif len(table_id) < 17 or len(table_id) > 18:
                    issues.append(f"{name} table ID should be 17-18 characters, got: {table_id} ({len(table_id)} chars)")

        if issues:
            messagebox.showwarning("ID Format Issues", "\n".join(issues))
        else:
            messagebox.showinfo("Validation", "All IDs are in correct format!")
            
    def on_table_select(self, event):
        """Handle table selection from list"""
        selection = self.tables_listbox.curselection()
        if selection:
            table_info = self.tables_listbox.get(selection[0])
            table_id = table_info.split(' - ')[-1]
            table_name = table_info.split(' - ')[0].lower()
            
            # Auto-assign based on table name
            if 'ghl' in table_name or 'gohighlevel' in table_name:
                self.ghl_table_id.set(table_id)
            elif 'google' in table_name and 'ads' in table_name:
                self.google_ads_table_id.set(table_id)
            elif 'meta' in table_name or 'facebook' in table_name:
                self.meta_ads_table_id.set(table_id)
            elif 'pos' in table_name or 'point' in table_name:
                self.pos_table_id.set(table_id)
                
    def add_location(self):
        """Add a location to the list"""
        location = self.location_entry.get().strip()
        if location and location not in self.locations:
            self.locations.append(location)
            self.update_locations_display()
            self.location_entry.delete(0, tk.END)
            
    def remove_location(self):
        """Remove selected location"""
        selection = self.locations_listbox.curselection()
        if selection:
            location = self.locations_listbox.get(selection[0])
            self.locations.remove(location)
            self.update_locations_display()
            
    def update_locations_display(self):
        """Update the locations listbox"""
        self.locations_listbox.delete(0, tk.END)
        for location in self.locations:
            self.locations_listbox.insert(tk.END, location)
            
    def validate_config(self):
        """Validate the current configuration"""
        self.validation_text.delete(1.0, tk.END)
        
        issues = []
        
        # Check required fields
        if not self.client_name.get():
            issues.append("❌ Client name is required")
        else:
            self.validation_text.insert(tk.END, "✅ Client name: " + self.client_name.get() + "\n")
            
        if not self.base_id.get():
            issues.append("❌ Base ID is required")
        else:
            self.validation_text.insert(tk.END, "✅ Base ID: " + self.base_id.get() + "\n")
            
        if not self.template_path.get() or not os.path.exists(self.template_path.get()):
            issues.append("❌ Template path does not exist")
        else:
            self.validation_text.insert(tk.END, "✅ Template path: " + self.template_path.get() + "\n")
            
        # Check enabled data sources have table IDs
        if self.enable_ghl.get() and not self.ghl_table_id.get():
            issues.append("❌ GHL is enabled but no table ID provided")
        elif self.enable_ghl.get():
            self.validation_text.insert(tk.END, "✅ GHL table ID: " + self.ghl_table_id.get() + "\n")
            
        if self.enable_google_ads.get() and not self.google_ads_table_id.get():
            issues.append("❌ Google Ads is enabled but no table ID provided")
        elif self.enable_google_ads.get():
            self.validation_text.insert(tk.END, "✅ Google Ads table ID: " + self.google_ads_table_id.get() + "\n")
            
        if self.enable_meta_ads.get() and not self.meta_ads_table_id.get():
            issues.append("❌ Meta Ads is enabled but no table ID provided")
        elif self.enable_meta_ads.get():
            self.validation_text.insert(tk.END, "✅ Meta Ads table ID: " + self.meta_ads_table_id.get() + "\n")
            
        if self.enable_pos.get() and not self.pos_table_id.get():
            issues.append("❌ POS is enabled but no table ID provided")
        elif self.enable_pos.get():
            self.validation_text.insert(tk.END, "✅ POS table ID: " + self.pos_table_id.get() + "\n")
            
        # Display issues
        if issues:
            self.validation_text.insert(tk.END, "\n🚨 ISSUES FOUND:\n")
            for issue in issues:
                self.validation_text.insert(tk.END, issue + "\n")
        else:
            self.validation_text.insert(tk.END, "\n🎉 Configuration is valid! Ready to generate dashboard.\n")
            
        # Display enabled data sources
        enabled_sources = []
        if self.enable_ghl.get():
            enabled_sources.append("GHL")
        if self.enable_google_ads.get():
            enabled_sources.append("Google Ads")
        if self.enable_meta_ads.get():
            enabled_sources.append("Meta Ads")
        if self.enable_pos.get():
            enabled_sources.append("POS")
            
        self.validation_text.insert(tk.END, f"\n📊 Enabled data sources: {', '.join(enabled_sources)}\n")
        self.validation_text.insert(tk.END, f"📍 Locations: {', '.join(self.locations)}\n")

    def generate_dashboard(self):
        """Generate the customized dashboard"""
        # First validate
        self.validate_config()

        # Check if validation passed
        validation_content = self.validation_text.get(1.0, tk.END)
        if "❌" in validation_content:
            messagebox.showerror("Error", "Please fix validation issues before generating")
            return

        try:
            # Create output directory
            client_folder = self.client_name.get().lower().replace(' ', '_')
            output_dir = os.path.join(self.output_path.get(), f"{client_folder}_dashboard")

            if os.path.exists(output_dir):
                if not messagebox.askyesno("Overwrite", f"Directory {output_dir} exists. Overwrite?"):
                    return
                shutil.rmtree(output_dir)

            # Copy template files
            shutil.copytree(self.template_path.get(), output_dir)

            # Update configuration files
            self.update_config_py(output_dir)
            self.update_server_py(output_dir)
            self.update_script_js(output_dir)
            self.update_html_file(output_dir)
            self.create_env_file(output_dir)
            self.create_readme(output_dir)

            # Verify the generated files
            verification_result = self.verify_generated_files(output_dir)

            if verification_result['success']:
                self.validation_text.insert(tk.END, f"\n🎉 Dashboard generated successfully!\n")
                self.validation_text.insert(tk.END, f"📁 Location: {output_dir}\n")
                self.validation_text.insert(tk.END, f"✅ Verification: All files are accurate\n")

                messagebox.showinfo("Success", f"Dashboard generated and verified successfully!\nLocation: {output_dir}")
            else:
                self.validation_text.insert(tk.END, f"\n⚠️ Dashboard generated with issues:\n")
                for issue in verification_result['issues']:
                    self.validation_text.insert(tk.END, f"❌ {issue}\n")

                messagebox.showwarning("Generated with Issues",
                    f"Dashboard generated but verification found issues:\n" +
                    "\n".join(verification_result['issues']))

        except Exception as e:
            messagebox.showerror("Error", f"Error generating dashboard: {str(e)}")

    def update_config_py(self, output_dir):
        """Update config.py with client-specific settings"""
        config_path = os.path.join(output_dir, 'config.py')

        # Read the current config with proper encoding
        with open(config_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Build table configuration
        fresh_tables = {}

        if self.enable_ghl.get():
            fresh_tables['ghl'] = {
                'id': self.ghl_table_id.get(),
                'name': f'{self.client_name.get()} GHL',
                'date_field': 'Date Created',
                'sort_direction': 'desc'
            }

        if self.enable_google_ads.get():
            fresh_tables['google_ads'] = {
                'id': self.google_ads_table_id.get(),
                'name': f'{self.client_name.get()} Google Ads',
                'date_field': 'Date',
                'sort_direction': 'desc'
            }

        if self.enable_meta_ads.get():
            fresh_tables['meta_ads'] = {
                'id': self.meta_ads_table_id.get(),
                'name': f'{self.client_name.get()} Meta Ads',
                'date_field': 'Reporting ends',
                'sort_direction': 'desc'
            }

        if self.enable_pos.get():
            fresh_tables['pos'] = {
                'id': self.pos_table_id.get(),
                'name': f'{self.client_name.get()} POS',
                'date_field': 'Created',
                'sort_direction': 'desc'
            }

        # Replace FRESH_TABLES section
        fresh_tables_str = "    FRESH_TABLES = {\n"
        for key, config in fresh_tables.items():
            fresh_tables_str += f"        '{key}': {{\n"
            fresh_tables_str += f"            'id': '{config['id']}',\n"
            fresh_tables_str += f"            'name': '{config['name']}',\n"
            fresh_tables_str += f"            'date_field': '{config['date_field']}',\n"
            fresh_tables_str += f"            'sort_direction': '{config['sort_direction']}'\n"
            fresh_tables_str += "        },\n"
        fresh_tables_str += "    }"

        # Use a more precise approach to replace FRESH_TABLES section
        # Find the start and end of the FRESH_TABLES block
        start_pattern = r'FRESH_TABLES = \{'
        start_match = re.search(start_pattern, content)

        if start_match:
            # Find the matching closing brace
            start_pos = start_match.start()
            brace_count = 0
            pos = start_match.end() - 1  # Start from the opening brace

            for i in range(pos, len(content)):
                if content[i] == '{':
                    brace_count += 1
                elif content[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_pos = i + 1
                        break
            else:
                # Fallback to simple replacement if we can't find matching brace
                pattern = r'FRESH_TABLES = \{[^}]*\}'
                content = re.sub(pattern, fresh_tables_str.strip(), content, flags=re.DOTALL)

            # Replace the entire FRESH_TABLES block
            if 'end_pos' in locals():
                content = content[:start_pos] + fresh_tables_str.strip() + content[end_pos:]

        # Write updated config with proper encoding
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def update_server_py(self, output_dir):
        """Update server.py with client base ID"""
        server_path = os.path.join(output_dir, 'server.py')

        with open(server_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Replace base ID more precisely
        pattern = r"base_id = 'app[A-Za-z0-9]{14}'"
        replacement = f"base_id = '{self.base_id.get()}'"
        content = re.sub(pattern, replacement, content)

        with open(server_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def update_script_js(self, output_dir):
        """Update script.js with centralized client configuration"""
        script_path = os.path.join(output_dir, 'script.js')

        with open(script_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Build enabled sources list
        enabled_sources = []
        if self.enable_ghl.get():
            enabled_sources.append('ghl')
        if self.enable_google_ads.get():
            enabled_sources.append('googleAds')
        if self.enable_meta_ads.get():
            enabled_sources.append('metaAds')
        if self.enable_pos.get():
            enabled_sources.append('pos')

        # Build disabled sources list
        all_sources = ['ghl', 'googleAds', 'metaAds', 'pos']
        disabled_sources = [src for src in all_sources if src not in enabled_sources]

        # Build table configuration
        tables_config = {}
        tables_config['ghl'] = self.ghl_table_id.get() if self.enable_ghl.get() else 'null'
        tables_config['googleAds'] = self.google_ads_table_id.get() if self.enable_google_ads.get() else 'null'
        tables_config['pos'] = self.pos_table_id.get() if self.enable_pos.get() else 'null'
        tables_config['metaAds'] = self.meta_ads_table_id.get() if self.enable_meta_ads.get() else 'null'

        # Create the new CLIENT_CONFIG section
        client_config = f"""// ===== CENTRALIZED CLIENT CONFIGURATION =====
// All client-specific settings in one place
const CLIENT_CONFIG = {{
    // Client Information
    clientName: '{self.client_name.get()}',
    businessName: '{self.business_name.get() or self.client_name.get()}',

    // Locations
    locations: {str(self.locations).replace("'", "'")},

    // Airtable Configuration
    airtable: {{
        baseId: '{self.base_id.get()}',
        tables: {{
            ghl: {'null' if not self.enable_ghl.get() else "'" + self.ghl_table_id.get() + "'"},                 // {self.client_name.get()} GHL table
            googleAds: {'null' if not self.enable_google_ads.get() else "'" + self.google_ads_table_id.get() + "'"},           // {self.client_name.get()} Google Ads table
            pos: {'null' if not self.enable_pos.get() else "'" + self.pos_table_id.get() + "'"},                                 // POS {'enabled' if self.enable_pos.get() else 'disabled'} for {self.client_name.get()}
            metaAds: {'null' if not self.enable_meta_ads.get() else "'" + self.meta_ads_table_id.get() + "'"}                             // Meta Ads {'enabled' if self.enable_meta_ads.get() else 'disabled'} for {self.client_name.get()}
        }}
    }},

    // Enabled Data Sources
    enabledSources: {str(enabled_sources).replace("'", "'")},
    disabledSources: {str(disabled_sources).replace("'", "'")}
}};

// Legacy compatibility - keep existing AIRTABLE_CONFIG for backward compatibility
const AIRTABLE_CONFIG = {{
    baseId: CLIENT_CONFIG.airtable.baseId,
    tables: CLIENT_CONFIG.airtable.tables
}};"""

        # Replace the CLIENT_CONFIG section or AIRTABLE_CONFIG section
        if 'CLIENT_CONFIG' in content:
            # Replace existing CLIENT_CONFIG
            pattern = r'// ===== CENTRALIZED CLIENT CONFIGURATION =====.*?const AIRTABLE_CONFIG = \{[^}]*\};'
            content = re.sub(pattern, client_config, content, flags=re.DOTALL)
        else:
            # Replace old AIRTABLE_CONFIG with new CLIENT_CONFIG
            pattern = r'// Airtable configuration.*?const AIRTABLE_CONFIG = \{[^}]*\};'
            content = re.sub(pattern, client_config, content, flags=re.DOTALL)

        # Replace hardcoded locations
        old_locations = ['Quick Fix - Foley', 'Quick Fix - Mobile', 'Quick Fix - Daphne']
        new_locations = [f"{self.business_name.get() or self.client_name.get()} - {loc}" for loc in self.locations]

        # Replace the hardcoded locations array
        old_locations_str = str(old_locations).replace("'", "'")
        new_locations_str = f"CLIENT_CONFIG.locations.map(loc => `${{CLIENT_CONFIG.businessName}} - ${{loc}}`)"
        content = content.replace(old_locations_str, new_locations_str)

        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def update_html_file(self, output_dir):
        """Update HTML file to hide disabled data sources"""
        html_path = os.path.join(output_dir, 'index.html')

        with open(html_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Hide sections for disabled data sources
        if not self.enable_meta_ads.get():
            # Hide Meta Ads sections
            content = self.hide_html_section(content, 'meta-ads', 'Meta Ads')

        if not self.enable_pos.get():
            # Hide POS/Sales sections
            content = self.hide_html_section(content, 'sales', 'Sales')
            content = self.hide_html_section(content, 'pos', 'POS')

        # Update business name in title and headers
        if self.business_name.get():
            content = re.sub(
                r'<title>[^<]*</title>',
                f'<title>{self.business_name.get()} - Analytics Dashboard</title>',
                content
            )

        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def hide_html_section(self, content, section_id, section_name):
        """Hide HTML sections by adding display:none style"""
        # Hide by ID
        patterns = [
            rf'(<[^>]*id="[^"]*{section_id}[^"]*"[^>]*>)',
            rf'(<[^>]*class="[^"]*{section_id}[^"]*"[^>]*>)',
            rf'(<[^>]*{section_id}[^>]*>)'
        ]

        for pattern in patterns:
            content = re.sub(
                pattern,
                lambda m: m.group(1).replace('>', ' style="display:none;">'),
                content,
                flags=re.IGNORECASE
            )

        return content

    def create_env_file(self, output_dir):
        """Create .env.example file"""
        env_content = f"""# {self.client_name.get()} Analytics Dashboard - Environment Configuration

# Airtable Configuration
AIRTABLE_API_KEY=your_airtable_api_key_here
AIRTABLE_BASE_ID={self.base_id.get()}

# Table IDs - {self.client_name.get()}
"""

        if self.enable_ghl.get():
            env_content += f"AIRTABLE_GHL_TABLE_ID={self.ghl_table_id.get()}\n"
        if self.enable_google_ads.get():
            env_content += f"AIRTABLE_GOOGLE_ADS_TABLE_ID={self.google_ads_table_id.get()}\n"
        if self.enable_meta_ads.get():
            env_content += f"AIRTABLE_META_ADS_TABLE_ID={self.meta_ads_table_id.get()}\n"
        if self.enable_pos.get():
            env_content += f"AIRTABLE_POS_TABLE_ID={self.pos_table_id.get()}\n"

        env_content += f"""
# Server Configuration
FLASK_ENV=production
HOST=127.0.0.1
PORT=8000

# Business Configuration
BUSINESS_NAME={self.business_name.get() or self.client_name.get()}
LOCATIONS={','.join(self.locations)}

# Optional: Claude AI Configuration (if using AI features)
CLAUDE_API_KEY=your_claude_api_key_here

# Security Settings
CORS_ORIGINS=http://localhost:8000,http://127.0.0.1:8000
"""

        env_path = os.path.join(output_dir, '.env.example')
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(env_content)

    def create_readme(self, output_dir):
        """Create client-specific README"""
        enabled_sources = []
        if self.enable_ghl.get():
            enabled_sources.append("GHL (GoHighLevel)")
        if self.enable_google_ads.get():
            enabled_sources.append("Google Ads")
        if self.enable_meta_ads.get():
            enabled_sources.append("Meta Ads")
        if self.enable_pos.get():
            enabled_sources.append("POS (Point of Sale)")

        readme_content = f"""# {self.client_name.get()} Analytics Dashboard

This is a customized analytics dashboard specifically configured for **{self.business_name.get() or self.client_name.get()}** business data.

## 🏗️ Configuration

### Airtable Base Configuration
- **Base ID**: `{self.base_id.get()}`
- **Base Name**: {self.client_name.get()}

### Enabled Data Sources
{chr(10).join([f'- ✅ **{source}**' for source in enabled_sources])}

### Table Mappings
| Data Source | Table ID | Status |
|-------------|----------|--------|"""

        if self.enable_ghl.get():
            readme_content += f"\n| **GHL** | `{self.ghl_table_id.get()}` | ✅ Enabled |"
        if self.enable_google_ads.get():
            readme_content += f"\n| **Google Ads** | `{self.google_ads_table_id.get()}` | ✅ Enabled |"
        if self.enable_meta_ads.get():
            readme_content += f"\n| **Meta Ads** | `{self.meta_ads_table_id.get()}` | ✅ Enabled |"
        if self.enable_pos.get():
            readme_content += f"\n| **POS** | `{self.pos_table_id.get()}` | ✅ Enabled |"

        readme_content += f"""

### Business Locations
{chr(10).join([f'- {location}' for location in self.locations])}

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your Airtable API key
# AIRTABLE_API_KEY=your_actual_api_key_here
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run Development Server
```bash
python start_server.py
```

### 4. Access Dashboard
Open your browser to: `http://localhost:8000`

## 📊 Features

This dashboard includes only the enabled data sources for {self.client_name.get()}:
{chr(10).join([f'- **{source}**: Real-time analytics and reporting' for source in enabled_sources])}

## 🚢 Deployment

This dashboard is configured for Railway deployment. See DEPLOYMENT.md for detailed instructions.

---

**{self.client_name.get()} Analytics Dashboard**
*Generated by RL Dashboard Customizer*
*Enabled sources: {', '.join(enabled_sources)}*
"""

        readme_path = os.path.join(output_dir, f'README_{self.client_name.get().upper().replace(" ", "_")}.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)

    def save_config(self):
        """Save current configuration to JSON file"""
        config = {
            'client_name': self.client_name.get(),
            'business_name': self.business_name.get(),
            'base_id': self.base_id.get(),
            'template_path': self.template_path.get(),
            'output_path': self.output_path.get(),
            'table_ids': {
                'ghl': self.ghl_table_id.get(),
                'google_ads': self.google_ads_table_id.get(),
                'meta_ads': self.meta_ads_table_id.get(),
                'pos': self.pos_table_id.get()
            },
            'enabled_sources': {
                'ghl': self.enable_ghl.get(),
                'google_ads': self.enable_google_ads.get(),
                'meta_ads': self.enable_meta_ads.get(),
                'pos': self.enable_pos.get()
            },
            'locations': self.locations
        }

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json")],
            title="Save Configuration"
        )

        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            messagebox.showinfo("Success", "Configuration saved successfully!")

    def load_config(self):
        """Load configuration from JSON file"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json")],
            title="Load Configuration"
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Load basic settings
                self.client_name.set(config.get('client_name', ''))
                self.business_name.set(config.get('business_name', ''))
                self.base_id.set(config.get('base_id', ''))
                self.template_path.set(config.get('template_path', ''))
                self.output_path.set(config.get('output_path', ''))

                # Load table IDs
                table_ids = config.get('table_ids', {})
                self.ghl_table_id.set(table_ids.get('ghl', ''))
                self.google_ads_table_id.set(table_ids.get('google_ads', ''))
                self.meta_ads_table_id.set(table_ids.get('meta_ads', ''))
                self.pos_table_id.set(table_ids.get('pos', ''))

                # Load enabled sources
                enabled = config.get('enabled_sources', {})
                self.enable_ghl.set(enabled.get('ghl', True))
                self.enable_google_ads.set(enabled.get('google_ads', True))
                self.enable_meta_ads.set(enabled.get('meta_ads', False))
                self.enable_pos.set(enabled.get('pos', False))

                # Load locations
                self.locations = config.get('locations', ['Main Location'])
                self.update_locations_display()

                messagebox.showinfo("Success", "Configuration loaded successfully!")

            except Exception as e:
                messagebox.showerror("Error", f"Error loading configuration: {str(e)}")

    def verify_generated_files(self, output_dir):
        """Verify that generated files have correct IDs and configuration"""
        issues = []

        try:
            # Verify config.py
            config_path = os.path.join(output_dir, 'config.py')
            with open(config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()

            # Check base ID in config
            if self.base_id.get() not in config_content:
                issues.append(f"Base ID {self.base_id.get()} not found in config.py")

            # Check enabled table IDs
            if self.enable_ghl.get() and self.ghl_table_id.get() not in config_content:
                issues.append(f"GHL table ID {self.ghl_table_id.get()} not found in config.py")

            if self.enable_google_ads.get() and self.google_ads_table_id.get() not in config_content:
                issues.append(f"Google Ads table ID {self.google_ads_table_id.get()} not found in config.py")

            # Verify server.py
            server_path = os.path.join(output_dir, 'server.py')
            with open(server_path, 'r', encoding='utf-8') as f:
                server_content = f.read()

            if self.base_id.get() not in server_content:
                issues.append(f"Base ID {self.base_id.get()} not found in server.py")

            # Verify script.js
            script_path = os.path.join(output_dir, 'script.js')
            with open(script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()

            if self.base_id.get() not in script_content:
                issues.append(f"Base ID {self.base_id.get()} not found in script.js")

            if self.enable_ghl.get() and self.ghl_table_id.get() not in script_content:
                issues.append(f"GHL table ID {self.ghl_table_id.get()} not found in script.js")

            if self.enable_google_ads.get() and self.google_ads_table_id.get() not in script_content:
                issues.append(f"Google Ads table ID {self.google_ads_table_id.get()} not found in script.js")

            # Verify .env.example
            env_path = os.path.join(output_dir, '.env.example')
            with open(env_path, 'r', encoding='utf-8') as f:
                env_content = f.read()

            if self.base_id.get() not in env_content:
                issues.append(f"Base ID {self.base_id.get()} not found in .env.example")

            # Check for old/incorrect IDs that shouldn't be there
            old_base_patterns = ['app7ffftdM6e3yekG', 'appJWDQxzECcXvWz7']
            for old_id in old_base_patterns:
                if old_id in config_content or old_id in server_content or old_id in script_content:
                    issues.append(f"Found old base ID {old_id} in generated files")

            # Verify that disabled data sources are not referenced
            if not self.enable_meta_ads.get():
                meta_patterns = ['meta_ads', 'Meta Ads', 'metaAds']
                for pattern in meta_patterns:
                    if pattern in config_content and 'FRESH_TABLES' in config_content:
                        # Check if it's in the FRESH_TABLES section
                        fresh_start = config_content.find('FRESH_TABLES')
                        fresh_end = config_content.find('}', fresh_start) + 1
                        fresh_section = config_content[fresh_start:fresh_end]
                        if pattern in fresh_section:
                            issues.append(f"Meta Ads reference found in FRESH_TABLES but Meta Ads is disabled")

            if not self.enable_pos.get():
                pos_patterns = ['pos', 'POS']
                for pattern in pos_patterns:
                    if pattern in config_content and 'FRESH_TABLES' in config_content:
                        fresh_start = config_content.find('FRESH_TABLES')
                        fresh_end = config_content.find('}', fresh_start) + 1
                        fresh_section = config_content[fresh_start:fresh_end]
                        if pattern in fresh_section:
                            issues.append(f"POS reference found in FRESH_TABLES but POS is disabled")

            return {
                'success': len(issues) == 0,
                'issues': issues
            }

        except Exception as e:
            return {
                'success': False,
                'issues': [f"Verification failed: {str(e)}"]
            }

    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = RLDashboardCustomizer()
    app.run()
