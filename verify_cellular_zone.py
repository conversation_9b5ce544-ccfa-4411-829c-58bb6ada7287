#!/usr/bin/env python3
"""
Quick verification script for Cellular Zone dashboard
"""

import os
import sys

def verify_cellular_zone():
    dashboard_path = "test8/cellular_zone_dashboard"
    
    print("🔧 Verifying Cellular Zone Dashboard Configuration...")
    
    if not os.path.exists(dashboard_path):
        print(f"❌ Dashboard path not found: {dashboard_path}")
        return False
    
    # Check client-config.js
    client_config_path = os.path.join(dashboard_path, "client-config.js")
    if os.path.exists(client_config_path):
        with open(client_config_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if "Cellular Zone" in content:
                print("✅ client-config.js contains 'Cellular Zone'")
            else:
                print("❌ client-config.js does not contain 'Cellular Zone'")
                
            if "app9JgRBZC2GNlaKM" in content:
                print("✅ client-config.js contains correct base ID")
            else:
                print("❌ client-config.js does not contain correct base ID")
    else:
        print("❌ client-config.js not found")
    
    # Check server-config.py
    server_config_path = os.path.join(dashboard_path, "server-config.py")
    if os.path.exists(server_config_path):
        with open(server_config_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if "CLIENT_NAME = 'Cellular Zone'" in content:
                print("✅ server-config.py contains correct CLIENT_NAME")
            else:
                print("❌ server-config.py CLIENT_NAME issue")
                
            if "app9JgRBZC2GNlaKM" in content:
                print("✅ server-config.py contains correct base ID")
            else:
                print("❌ server-config.py does not contain correct base ID")
    else:
        print("❌ server-config.py not found")
    
    # Test Python import
    try:
        sys.path.insert(0, dashboard_path)
        from server_config import ClientConfig
        print(f"✅ server-config.py imports successfully")
        print(f"   Client Name: {ClientConfig.CLIENT_NAME}")
        print(f"   Base ID: {ClientConfig.AIRTABLE_BASE_ID}")
        print(f"   Enabled Sources: {ClientConfig.ENABLED_SOURCES}")
        
        fresh_tables = ClientConfig.get_fresh_tables()
        print(f"   Fresh Tables: {list(fresh_tables.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ server-config.py import failed: {e}")
        return False

if __name__ == "__main__":
    success = verify_cellular_zone()
    if success:
        print("\n🎉 Cellular Zone configuration verified successfully!")
        print("\nTo test the dashboard:")
        print("1. cd test8/cellular_zone_dashboard")
        print("2. python start_server.py")
        print("3. Open http://localhost:8000")
        print("4. Check console logs for 'Cellular Zone' and correct base ID")
    else:
        print("\n❌ Configuration verification failed!")
        print("Please check the issues above and regenerate the dashboard.")
