/**
 * CENTRALIZED CLIENT CONFIGURATION
 * Single source of truth for all client-specific settings
 * 
 * CUSTOMIZER INSTRUCTIONS:
 * - Replace CLIENT_NAME with actual client name
 * - Replace CLIENT_BASE_ID with client's Airtable base ID
 * - Replace table IDs with client's actual table IDs
 * - Set enabled/disabled data sources based on client needs
 * - Update locations array (will be auto-populated from GHL data)
 */

window.CLIENT_CONFIG = {
    // ===== CLIENT INFORMATION =====
    clientName: 'Quick Fix',
    businessName: 'Quick Fix',
    
    // ===== BUSINESS LOCATIONS =====
    // Note: Will be dynamically populated from GHL table data
    locations: [], // Auto-populated from actual GHL data
    
    // ===== AIRTABLE CONFIGURATION =====
    airtable: {
        baseId: 'app7ffftdM6e3yekG',

        // Table IDs - set to null to disable a data source
        tables: {
            ghl: 'tblcdFVUC3zJrbmNf',                    // GHL/GoHighLevel leads
            googleAds: 'tblRBXdh6L6zm9CZn',              // Google Ads campaigns
            pos: 'tblHyyZHUsTdEb3BL',                    // Point of Sale
            metaAds: 'tbl7mWcQBNA2TQAjc',               // Meta/Facebook Ads
            metaAdsSimplified: 'tblA6ABFBTURfyZx9',      // Meta Ads Simplified
            metaAdsSummary: 'tblIQXVSwtwq1P4W7',         // Meta Ads Summary
            metaAdsPerformance: 'tblm9kp1FStqAywPN'      // Meta Ads Performance
        }
    },
    
    // ===== DATA SOURCE CONFIGURATION =====
    dataSources: {
        // Enabled data sources (customizer will set based on client needs)
        enabled: ['ghl', 'googleAds', 'pos', 'metaAds', 'metaAdsSimplified', 'metaAdsSummary', 'metaAdsPerformance'],
        
        // Disabled data sources (will be hidden from UI)
        disabled: [],
        
        // Default date ranges for each source
        defaultDateRanges: {
            ghl: 'last-14',
            googleAds: 'last-30',
            pos: 'last-30',
            metaAds: 'last-30'
        }
    },
    
    // ===== UI CONFIGURATION =====
    ui: {
        // Dashboard title
        title: 'Quick Fix Analytics Dashboard',
        
        // Show/hide sections based on enabled data sources
        showSalesReport: true,      // POS enabled
        showMetaAdsReport: true,    // Meta Ads enabled
        showGoogleAdsReport: true,  // Google Ads enabled
        showLeadReport: true,       // GHL enabled
        
        // Location-based features
        enableLocationFiltering: true,
        defaultLocation: 'all'
    }
};

// ===== HELPER FUNCTIONS =====

/**
 * Check if a data source is enabled
 */
window.CLIENT_CONFIG.isEnabled = function(dataSource) {
    return this.dataSources.enabled.includes(dataSource) && 
           this.airtable.tables[dataSource] !== null &&
           this.airtable.tables[dataSource] !== 'null' &&
           this.airtable.tables[dataSource] !== '';
};

/**
 * Get table ID for a data source
 */
window.CLIENT_CONFIG.getTableId = function(dataSource) {
    const tableId = this.airtable.tables[dataSource];
    return (tableId && tableId !== 'null' && tableId !== '') ? tableId : null;
};

/**
 * Get formatted locations with business name
 */
window.CLIENT_CONFIG.getFormattedLocations = function() {
    return this.locations.map(location => `${this.businessName} - ${location}`);
};

/**
 * Get base ID
 */
window.CLIENT_CONFIG.getBaseId = function() {
    return this.airtable.baseId;
};

/**
 * Populate locations from GHL data
 */
window.CLIENT_CONFIG.populateLocationsFromData = function(ghlData) {
    if (!ghlData || !Array.isArray(ghlData)) {
        console.warn('[CONFIG] No GHL data provided for location population');
        return;
    }
    
    // Extract unique locations from GHL data
    const uniqueLocations = [...new Set(
        ghlData
            .map(record => record.Location || record.location)
            .filter(location => location && location.trim() !== '')
    )].sort();
    
    // Update the locations array
    this.locations = uniqueLocations;
    
    console.log('[CONFIG] Populated locations from GHL data:', this.locations);
    
    // Update location dropdowns in the UI
    this.updateLocationDropdowns();
    
    return this.locations;
};

/**
 * Update all location dropdowns in the UI
 */
window.CLIENT_CONFIG.updateLocationDropdowns = function() {
    const locationSelectors = [
        'location-filter',
        'report-location', 
        'compare-location',
        'location-performance-filter'
    ];
    
    locationSelectors.forEach(selectorId => {
        const dropdown = document.getElementById(selectorId);
        if (dropdown) {
            // Clear existing options except "All"
            dropdown.innerHTML = '<option value="all">All Locations</option>';
            
            // Add location options
            this.locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location;
                option.textContent = location;
                dropdown.appendChild(option);
            });
            
            console.log(`[CONFIG] Updated dropdown: ${selectorId} with ${this.locations.length} locations`);
        }
    });
};

// ===== CACHE MANAGEMENT =====
// Clear any cached data from previous configurations
if (typeof localStorage !== 'undefined') {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
        if (key.startsWith('rl_airtable_')) {
            localStorage.removeItem(key);
        }
    });
    console.log('[CONFIG] Cleared cached data for fresh configuration');
}

// ===== LEGACY COMPATIBILITY =====
// Keep existing AIRTABLE_CONFIG for backward compatibility
window.AIRTABLE_CONFIG = {
    baseId: window.CLIENT_CONFIG.getBaseId(),
    tables: {
        ghl: window.CLIENT_CONFIG.getTableId('ghl'),
        googleAds: window.CLIENT_CONFIG.getTableId('googleAds'),
        pos: window.CLIENT_CONFIG.getTableId('pos'),
        metaAds: window.CLIENT_CONFIG.getTableId('metaAds'),
        metaAdsSimplified: window.CLIENT_CONFIG.getTableId('metaAdsSimplified'),
        metaAdsSummary: window.CLIENT_CONFIG.getTableId('metaAdsSummary'),
        metaAdsPerformance: window.CLIENT_CONFIG.getTableId('metaAdsPerformance')
    }
};

console.log('[CONFIG] Client configuration loaded:', window.CLIENT_CONFIG.clientName);
console.log('[CONFIG] Enabled data sources:', window.CLIENT_CONFIG.dataSources.enabled);
console.log('[CONFIG] Base ID:', window.CLIENT_CONFIG.getBaseId());
