#!/usr/bin/env python3
"""
Test script to verify that the Airtable configuration fixes work correctly.
This script will generate a test dashboard and verify that all configuration files
contain the correct base ID and table IDs.
"""

import os
import sys
import json
import shutil
import tempfile
from pathlib import Path

# Add the current directory to Python path to import DBCust
sys.path.insert(0, os.getcwd())

from DBCust import DashboardCustomizer, ClientConfig, DataSourceConfig

def test_airtable_configuration_fix():
    """Test the Airtable configuration fix by generating a dashboard and verifying the results"""
    
    print("🧪 Testing Airtable Configuration Fix")
    print("=" * 50)
    
    # Test configuration based on Ready Set Repair
    test_config = ClientConfig(
        business_name="Test Ready Set Repair",
        dashboard_title="Test Attribution Dashboard", 
        locations=["Grand Road", "Ina Road"],
        airtable_base_id="appJWDQxzECcXvWz7",  # Ready Set Repair base ID
        enabled_data_sources=["ghl", "google_ads", "pos", "meta_ads"],
        disabled_data_sources=[],
        data_source_configs={
            "ghl": DataSourceConfig(
                table_id="tblgNNUwWlZ8iWZ0P",
                cache_ttl=300,
                date_field="Date Created",
                enabled=True
            ),
            "google_ads": DataSourceConfig(
                table_id="tblOg7U7VYjWTpYeu", 
                cache_ttl=300,
                date_field="Date",
                enabled=True
            ),
            "pos": DataSourceConfig(
                table_id="tblZGgaDBO9Lwl4PD",
                cache_ttl=300,
                date_field="Created",
                enabled=True
            ),
            "meta_ads": DataSourceConfig(
                table_id="tbloHgJm0NloiC0V8",
                cache_ttl=300,
                date_field="Reporting ends",
                enabled=True
            )
        }
    )
    
    # Create temporary output directory
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = os.path.join(temp_dir, "test_dashboard")
        
        print(f"📁 Creating test dashboard in: {output_dir}")
        
        # Initialize customizer
        customizer = DashboardCustomizer()
        
        # Use current directory as source (contains the template files)
        source_dir = os.getcwd()
        
        # Generate the dashboard
        success = customizer.customize_dashboard(source_dir, output_dir, test_config)
        
        if not success:
            print("❌ Dashboard generation failed!")
            return False
        
        print("✅ Dashboard generated successfully!")
        
        # Verify the configuration
        verification_results = verify_generated_configuration(output_dir, test_config)
        
        if verification_results["success"]:
            print("🎉 ALL TESTS PASSED! Airtable configuration fix is working correctly!")
            return True
        else:
            print("❌ TESTS FAILED! Configuration issues found:")
            for error in verification_results["errors"]:
                print(f"   - {error}")
            return False

def verify_generated_configuration(output_dir: str, expected_config: ClientConfig) -> dict:
    """Verify that the generated files contain the correct Airtable configuration"""
    
    results = {
        "success": True,
        "errors": [],
        "verified_files": []
    }
    
    print("\n🔍 Verifying generated configuration files...")
    
    # Check config.py
    config_py_path = os.path.join(output_dir, 'config.py')
    if os.path.exists(config_py_path):
        print("   📄 Checking config.py...")
        with open(config_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verify base ID
        if expected_config.airtable_base_id not in content:
            results["errors"].append(f"Base ID {expected_config.airtable_base_id} not found in config.py")
            results["success"] = False
        else:
            print(f"      ✅ Base ID {expected_config.airtable_base_id} found")
        
        # Verify table IDs
        for source, source_config in expected_config.data_source_configs.items():
            if source_config.table_id not in content:
                results["errors"].append(f"Table ID {source_config.table_id} for {source} not found in config.py")
                results["success"] = False
            else:
                print(f"      ✅ {source} table ID {source_config.table_id} found")
        
        results["verified_files"].append("config.py")
    
    # Check script.js
    script_js_path = os.path.join(output_dir, 'script.js')
    if os.path.exists(script_js_path):
        print("   📄 Checking script.js...")
        with open(script_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verify base ID in AIRTABLE_CONFIG
        if f"baseId: '{expected_config.airtable_base_id}'" not in content:
            results["errors"].append(f"Base ID {expected_config.airtable_base_id} not found in script.js")
            results["success"] = False
        else:
            print(f"      ✅ Base ID {expected_config.airtable_base_id} found in AIRTABLE_CONFIG")
        
        results["verified_files"].append("script.js")
    
    # Check server.py
    server_py_path = os.path.join(output_dir, 'server.py')
    if os.path.exists(server_py_path):
        print("   📄 Checking server.py...")
        with open(server_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verify base ID
        if expected_config.airtable_base_id not in content:
            results["errors"].append(f"Base ID {expected_config.airtable_base_id} not found in server.py")
            results["success"] = False
        else:
            print(f"      ✅ Base ID {expected_config.airtable_base_id} found in server.py")
        
        results["verified_files"].append("server.py")
    
    # Check client_config.json
    client_config_path = os.path.join(output_dir, 'client_config.json')
    if os.path.exists(client_config_path):
        print("   📄 Checking client_config.json...")
        try:
            with open(client_config_path, 'r', encoding='utf-8') as f:
                client_config_data = json.load(f)
            
            # Verify base ID
            if client_config_data.get('airtable_base_id') != expected_config.airtable_base_id:
                results["errors"].append("Base ID mismatch in client_config.json")
                results["success"] = False
            else:
                print(f"      ✅ Base ID {expected_config.airtable_base_id} found in client_config.json")
            
            results["verified_files"].append("client_config.json")
        except json.JSONDecodeError:
            results["errors"].append("client_config.json is not valid JSON")
            results["success"] = False
    
    print(f"\n📊 Verification Summary:")
    print(f"   Files checked: {len(results['verified_files'])}")
    print(f"   Errors found: {len(results['errors'])}")
    
    return results

if __name__ == "__main__":
    success = test_airtable_configuration_fix()
    sys.exit(0 if success else 1)
