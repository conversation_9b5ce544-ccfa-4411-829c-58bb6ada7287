# RL-Test Template Customization Tool Design

## Overview
A Python application that automatically customizes the rl-test template for different Airtable bases and clients. This tool will replace hardcoded base and table IDs with client-specific ones, enabling rapid deployment of customized analytics dashboards.

## Architecture

### 1. Core Components

#### A. Configuration Manager
```python
class AirtableConfigManager:
    """Manages Airtable base and table configurations"""
    
    def fetch_base_structure(self, base_id: str) -> dict
    def map_tables_to_standard_names(self, tables: list) -> dict
    def validate_table_structure(self, table_id: str, expected_fields: list) -> bool
```

#### B. Template Processor
```python
class TemplateProcessor:
    """Processes and modifies template files"""
    
    def scan_template_files(self, template_path: str) -> dict
    def replace_ids_in_file(self, file_path: str, id_mapping: dict) -> bool
    def update_config_py(self, config_path: str, table_mapping: dict) -> bool
    def update_script_js(self, script_path: str, base_id: str, table_mapping: dict) -> bool
    def update_server_py(self, server_path: str, base_id: str) -> bool
```

#### C. Client Customizer
```python
class ClientCustomizer:
    """Main orchestrator for client customization"""
    
    def customize_template(self, config: CustomizationConfig) -> bool
    def generate_deployment_files(self, output_path: str) -> bool
    def create_env_example(self, output_path: str) -> bool
```

### 2. Configuration Format

#### A. Input Configuration (JSON)
```json
{
    "client_info": {
        "name": "Ready Set Repair",
        "business_name": "Ready Set Repair",
        "output_directory": "./ready_set_repair_dashboard"
    },
    "airtable_config": {
        "base_id": "appJWDQxzECcXvWz7",
        "api_key": "pat_xxx...",
        "table_mapping": {
            "ghl": "tblgNNUwWlZ8iWZ0P",
            "pos": "tblZGgaDBO9Lwl4PD",
            "google_ads": "tblOg7U7VYjWTpYeu",
            "meta_ads": "tbloHgJm0NloiC0V8"
        }
    },
    "template_config": {
        "source_template": "./rl-test-template",
        "enabled_data_sources": ["ghl", "pos", "google_ads", "meta_ads"],
        "disabled_data_sources": []
    }
}
```

#### B. Auto-Detection Mode
```json
{
    "client_info": {
        "name": "Auto Client",
        "business_name": "Auto Detected Client"
    },
    "airtable_config": {
        "base_id": "appJWDQxzECcXvWz7",
        "api_key": "pat_xxx...",
        "auto_detect_tables": true
    }
}
```

### 3. File Modification Strategy

#### A. ID Replacement Patterns
```python
REPLACEMENT_PATTERNS = {
    'base_id': {
        'pattern': r"baseId:\s*['\"]app[A-Za-z0-9]{14}['\"]",
        'files': ['script.js', 'server.py']
    },
    'table_ids': {
        'pattern': r"['\"]tbl[A-Za-z0-9]{14}['\"]",
        'files': ['config.py', 'script.js']
    }
}
```

#### B. Smart Table Mapping
```python
TABLE_DETECTION_RULES = {
    'ghl': {
        'required_fields': ['Contact Name', 'Date Created', 'Pipeline', 'Stage'],
        'optional_fields': ['Lead Value', 'Traffic Source', 'Channel']
    },
    'pos': {
        'required_fields': ['Name', 'Created', 'Ticket Amount'],
        'optional_fields': ['Company', 'Location', 'Phone']
    },
    'google_ads': {
        'required_fields': ['Date', 'Campaign Name', 'Cost', 'Clicks'],
        'optional_fields': ['Impressions', 'CTR', 'CPC']
    },
    'meta_ads': {
        'required_fields': ['Reporting starts', 'Ad name', 'Amount spent (USD)'],
        'optional_fields': ['Reach', 'Impressions', 'Results']
    }
}
```

### 4. Tool Interface

#### A. Command Line Interface
```bash
# Basic usage
python rl_customizer.py --config client_config.json

# Auto-detection mode
python rl_customizer.py --base-id appJWDQxzECcXvWz7 --api-key pat_xxx --client-name "Ready Set Repair"

# Interactive mode
python rl_customizer.py --interactive

# Validation only
python rl_customizer.py --config client_config.json --validate-only
```

#### B. GUI Interface (Optional)
- Simple tkinter interface for non-technical users
- Base ID input field
- API key input field
- Client name input field
- Auto-detect tables button
- Generate dashboard button

### 5. Output Structure

#### A. Generated Project Structure
```
ready_set_repair_dashboard/
├── config.py                 # Updated with client table IDs
├── server.py                 # Updated with client base ID
├── script.js                 # Updated with all client IDs
├── index.html                # Client branding applied
├── styles.css                # Optional: client color scheme
├── .env.example              # Template environment file
├── requirements.txt          # Dependencies
├── Dockerfile                # Deployment configuration
├── railway.json              # Railway deployment config
├── DEPLOYMENT.md             # Client-specific deployment guide
└── README_CLIENT.md          # Client-specific setup instructions
```

#### B. Generated Documentation
- Client-specific deployment instructions
- Environment variable setup guide
- Table structure validation report
- Troubleshooting guide

### 6. Error Handling & Validation

#### A. Pre-Generation Validation
- Verify Airtable API connectivity
- Validate base access permissions
- Check table structure compatibility
- Verify required fields exist

#### B. Post-Generation Validation
- Verify all ID replacements were successful
- Test configuration file syntax
- Validate JavaScript syntax
- Check deployment file completeness

### 7. Integration with DBCust

#### A. DBCust Enhancement
```python
# Add to DBCust.py
def generate_rl_dashboard(self):
    """Generate RL-Test dashboard using customization tool"""
    
    config = {
        "client_info": {
            "name": self.client_name,
            "business_name": self.business_name
        },
        "airtable_config": {
            "base_id": self.selected_base_id,
            "table_mapping": self.validated_table_mapping
        }
    }
    
    customizer = RLTestCustomizer()
    success = customizer.customize_template(config)
    
    if success:
        self.show_success_message("Dashboard generated successfully!")
    else:
        self.show_error_message("Dashboard generation failed!")
```

### 8. Future Enhancements

#### A. Template Versioning
- Support multiple rl-test template versions
- Automatic template updates
- Backward compatibility handling

#### B. Advanced Customization
- Custom color schemes
- Logo integration
- Custom field mappings
- Additional data source support

#### C. Deployment Integration
- Direct Railway deployment
- GitHub repository creation
- Automated CI/CD setup

This architecture provides a robust, scalable solution for customizing the rl-test template for different clients while maintaining code quality and deployment readiness.
