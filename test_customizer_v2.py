#!/usr/bin/env python3
"""
Test script for the new RL Dashboard Customizer v2.0
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from rl_dashboard_customizer_v2 import RLDashboardCustomizerV2
    print("✅ Successfully imported RLDashboardCustomizerV2")
    
    # Test instantiation
    app = RLDashboardCustomizerV2()
    print("✅ Successfully created customizer instance")
    
    # Test validation methods
    print("✅ Testing validation methods...")
    enabled_sources = app.get_enabled_sources()
    disabled_sources = app.get_disabled_sources()
    print(f"Default enabled sources: {enabled_sources}")
    print(f"Default disabled sources: {disabled_sources}")
    
    print("✅ All tests passed! Customizer v2.0 is ready to use.")
    print("\nTo run the GUI, execute: python rl_dashboard_customizer_v2.py")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
